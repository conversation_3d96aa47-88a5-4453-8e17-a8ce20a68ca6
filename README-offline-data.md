# Pokemon Game - Offline Data Setup

This guide explains how to set up offline data caching for the Pokemon game to reduce API dependency and enable offline play.

## Overview

The offline data system allows the game to:
- Work without internet connection once data is downloaded
- Reduce load on PokeAPI servers
- Provide faster loading times
- Maintain the same game functionality

## Setup Instructions

### 1. Download Pokemon Data

First, download all necessary Pokemon data from PokeAPI:

```bash
# Download first generation Pokemon (1-151)
node scripts/download-pokemon-data.js

# Or download a specific range (e.g., first 50 Pokemon)
node scripts/download-pokemon-data.js 50
```

This will create the following structure:
```
data/
├── pokemon/           # Individual Pokemon data files
├── species/           # Species data with evolution chains
├── evolution-chains/  # Evolution chain data
├── moves/            # Move data files
└── metadata/         # Download information
```

### 2. Generate Index Files

Create index files for faster data lookups:

```bash
node scripts/generate-indexes.js
```

This creates index files in `data/metadata/` for quick Pokemon, species, moves, and evolution chain lookups.

### 3. Verify Setup

The game will automatically detect and use offline data when available. Check the browser console for messages like:

```
✅ Offline Pokemon data available
📊 Offline data: 151 Pokemon, 165 moves
```

## Data Structure

### Pokemon Files
- **Location**: `data/pokemon/`
- **Format**: `001-bulbasaur.json`, `002-ivysaur.json`, etc.
- **Contains**: Stats, types, sprites, moves list

### Species Files
- **Location**: `data/species/`
- **Format**: `001-bulbasaur-species.json`
- **Contains**: Evolution chain URLs

### Evolution Chains
- **Location**: `data/evolution-chains/`
- **Format**: `chain-1.json`, `chain-2.json`, etc.
- **Contains**: Complete evolution chain data

### Moves
- **Location**: `data/moves/`
- **Format**: `tackle.json`, `vine-whip.json`, etc.
- **Contains**: Power, accuracy, PP, type, description

### Index Files
- **Location**: `data/metadata/`
- **Files**: 
  - `pokemon-index.json` - Quick Pokemon lookup
  - `species-index.json` - Species lookup
  - `moves-index.json` - Move lookup
  - `evolution-chains-index.json` - Evolution chain lookup
  - `download-info.json` - Download metadata

## How It Works

1. **Automatic Detection**: The game automatically detects if offline data is available
2. **Offline First**: When offline data exists, it's used instead of API calls
3. **API Fallback**: If offline data is missing or corrupted, the game falls back to PokeAPI
4. **Same Interface**: The offline system maintains the same data structure as the API

## Updating Data

To update the offline data:

1. Delete the `data/` directory
2. Run the download script again: `node scripts/download-pokemon-data.js`
3. Regenerate indexes: `node scripts/generate-indexes.js`

## File Sizes

Approximate file sizes for first generation Pokemon:
- **Pokemon data**: ~2MB (151 files)
- **Species data**: ~150KB (151 files)
- **Evolution chains**: ~50KB (78 files)
- **Moves**: ~500KB (165 files)
- **Total**: ~3MB

## Troubleshooting

### No Offline Data Detected
- Ensure the `data/` directory exists in the project root
- Check that `download-info.json` exists in `data/metadata/`
- Verify the offline data loader script is included in HTML

### API Fallback Messages
- This is normal behavior when offline data is incomplete
- The game will work but may be slower due to API calls

### Download Errors
- Check internet connection
- PokeAPI may be rate limiting - the script includes delays
- Try downloading a smaller range first (e.g., 50 Pokemon)

## Development Notes

### Adding New Pokemon
To support Pokemon beyond generation 1:
1. Update `CONFIG.API.POKEMON_LIMIT` in `js/config.js`
2. Run download script with higher limit
3. Regenerate indexes

### Custom Data Fields
To include additional data fields:
1. Modify the download script (`scripts/download-pokemon-data.js`)
2. Update the offline data loader (`js/offline-data-loader.js`)
3. Re-download data

### Performance Optimization
- Index files enable O(1) lookups instead of scanning directories
- Data is cached in memory after first load
- Only essential fields are stored to minimize file sizes

## Browser Compatibility

The offline data system works in all modern browsers that support:
- Fetch API
- Promises/async-await
- Local file loading via HTTP server

Note: Files must be served via HTTP/HTTPS (not file:// protocol) for security reasons.
