<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pokemon Battle Game - Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Pokemon Battle Game - Test Suite</h1>
        
        <div class="test-result info">
            <strong>Testing basic functionality...</strong>
        </div>
        
        <div id="test-results"></div>
        
        <h3>Console Output:</h3>
        <div id="console-output"></div>
        
        <div>
            <button onclick="runTests()">Run Tests</button>
            <button onclick="clearConsole()">Clear Console</button>
            <button onclick="testAPI()">Test API</button>
            <button onclick="testPokemon()">Test Pokemon Creation</button>
        </div>
    </div>

    <!-- Include our game scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/pokemon.js"></script>

    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function logToPage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            consoleOutput.textContent += `${timestamp} ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPage(args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };

        function addTestResult(message, success = true) {
            const resultsDiv = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${success ? '✓' : '✗'}</strong> ${message}`;
            resultsDiv.appendChild(div);
        }

        function clearConsole() {
            consoleOutput.textContent = '';
            document.getElementById('test-results').innerHTML = '';
        }

        async function runTests() {
            clearConsole();
            console.log('Starting test suite...');
            
            // Test 1: Check if CONFIG is loaded
            try {
                if (typeof CONFIG !== 'undefined') {
                    addTestResult('CONFIG object loaded successfully');
                    console.log('CONFIG.API.BASE_URL:', CONFIG.API.BASE_URL);
                } else {
                    addTestResult('CONFIG object not found', false);
                }
            } catch (error) {
                addTestResult(`CONFIG test failed: ${error.message}`, false);
            }

            // Test 2: Check if PokemonAPI is available
            try {
                if (typeof pokemonAPI !== 'undefined') {
                    addTestResult('PokemonAPI instance created successfully');
                    console.log('PokemonAPI cache size:', pokemonAPI.cache.size);
                } else {
                    addTestResult('PokemonAPI instance not found', false);
                }
            } catch (error) {
                addTestResult(`PokemonAPI test failed: ${error.message}`, false);
            }

            // Test 3: Check if Pokemon class is available
            try {
                if (typeof Pokemon !== 'undefined') {
                    addTestResult('Pokemon class loaded successfully');
                } else {
                    addTestResult('Pokemon class not found', false);
                }
            } catch (error) {
                addTestResult(`Pokemon class test failed: ${error.message}`, false);
            }

            // Test 4: Test type effectiveness function
            try {
                const effectiveness = getTypeEffectiveness('fire', ['grass']);
                if (effectiveness === 2) {
                    addTestResult('Type effectiveness calculation working (Fire vs Grass = 2x)');
                } else {
                    addTestResult(`Type effectiveness incorrect: expected 2, got ${effectiveness}`, false);
                }
            } catch (error) {
                addTestResult(`Type effectiveness test failed: ${error.message}`, false);
            }

            console.log('Basic tests completed.');
        }

        async function testAPI() {
            console.log('Testing API connection...');
            
            try {
                const pikachu = await pokemonAPI.fetchPokemon(25);
                addTestResult(`API test successful: Loaded ${pikachu.name} (ID: ${pikachu.id})`);
                console.log('Pikachu data:', pikachu);
            } catch (error) {
                addTestResult(`API test failed: ${error.message}`, false);
                console.error('API error:', error);
            }
        }

        async function testPokemon() {
            console.log('Testing Pokemon creation...');
            
            try {
                const charizard = await createPokemonFromId(6, 50);
                if (charizard) {
                    addTestResult(`Pokemon creation successful: ${charizard.name} (Level ${charizard.level})`);
                    console.log('Charizard stats:', charizard.stats);
                    console.log('Charizard moves:', charizard.moves.map(m => m.name));
                } else {
                    addTestResult('Pokemon creation failed: returned null', false);
                }
            } catch (error) {
                addTestResult(`Pokemon creation failed: ${error.message}`, false);
                console.error('Pokemon creation error:', error);
            }
        }

        // Run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
