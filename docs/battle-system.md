Kampfsystem-Regeln und Mechaniken
Grundlegendes Rundenbasiertes System

Kampfablauf:

    Initialisierung: Beide Trainer wählen ihr erstes Pokemon

    Aktionsauswahl: Spieler wählt zwischen Angriff, <PERSON><PERSON><PERSON>, Item benutzen oder Flucht

    Prioritätsberechnung: Reihenfolge basierend auf Pokemon-Initiative und Attacken-Priorität

    Aktionsausführung: Schäden berechnen, Status-Effekte anwenden

    Rundenende: KP prüfen, besiegte Pokemon austauschen

    Kampfende: Gewinner ermitteln wenn alle Pokemon eines Trainers besiegt sind

Schadensberechnung

Die vereinfachte Schadensformel für Ihr Spiel:

Basis-Schaden = ((Level × 2 ÷ 5 + 2) × Attackenstärke × Angriff ÷ Verteidigung ÷ 50 + 2)
Finaler Schaden = Basis-Schaden × Typ-Effektivität × STAB × Zufallsfaktor (0.85-1.0)

STAB (Same Type Attack Bonus): 1.5× Schaden wenn Pokemon-Typ = Attacken-Typ

Das Typ-System bildet das Kernstück der strategischen Tiefe. Super-effektive Attacken verursachen doppelten Schaden, während nicht sehr effektive Attacken nur halben Schaden anrichten.

Interface / UI:

Kampf-Interface Elemente

Hauptkampfansicht:

    Pokemon-Sprites (Spieler hinten, Gegner vorne)

    KP-Balken mit numerischen Werten

    Level- und Namensanzeigen

    Hintergrund je nach Kampfort

Aktions-Menü:

    Kampf: Zeigt verfügbare Attacken (4 Slots)

    Pokemon: Wechsel zu anderem Pokemon im Team

    Beutel: Items verwenden (Heiltränke, etc.)

    Flucht: Aus Wildpokemon-Kämpfen fliehen

Attackenauswahl:

    Attackenname und Typ-Icon

    Verbleibende AP (Angriffspunkte)

    Typ-Effektivitäts-Hinweise

Advanced effects in Battles:
Erweiterte Features und Status-Effekte
Status-Bedingungen

Primäre Status:

    Vergiftung: Verliert jede Runde 1/8 der max. KP

    Verbrennung: Verliert 1/8 KP, halbiert physischen Angriff

    Paralyse: 25% Chance pro Runde nicht angreifen zu können

    Schlaf: 1-3 Runden inaktiv

    Einfrieren: Inaktiv bis aufgetaut (20% Chance pro Runde)

Sekundäre Status:

    Verwirrung: 50% Chance sich selbst zu verletzen

    Anlocken: Kann nicht gewechselt werden

    Fluch: Verschiedene Effekte je nach Pokemon-Typ
