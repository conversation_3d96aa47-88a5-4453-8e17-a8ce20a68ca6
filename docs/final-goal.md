Das **finale Ziel** ist die vollständige Entwicklung eines **Pokemon-Kampfspiels** als webbasierte Anwendung (HTML, CSS, JavaScript), das nach Fertigstellung mit **Capacitor** in eine **Android-App** verpackt und über Google Play nutzbar gemacht werden soll .

Es sollen die **ersten 151 Pokemon** mit allen relevanten Eigenschaften wie Typen, Namen, ID, Basiswerten (HP, Angriff, Verteidigung usw.) und Attacken beinhaltet werden. Die Daten dafür werden aus offiziellen, zuverlässigen APIs (z. B. PokeAPI) heruntergeladen und als JSON in die Anwendung integriert. Auch visuelle Assets wie Pokemon-Sprites und Trainer-Bilder werden automatisiert eingebunden.

Das **Kern-Feature** ist ein **rundenbasiertes Kampfsystem**, das die Offizial-Regeln für Pokemon-Kämpfe so nah wie möglich abbildet, inklusive Typ-Effektivitäten, Schadensberechnung, Status-Effekt<PERSON>, He<PERSON>n, Wechseln der Pokemon und Fluchtoptionen. Der Spieler tritt gegen **NPC-Gegner mit unterschiedlichen KI-Stufen** (leicht, mittel, schwer) an.

**Das Ergebnis** ist eine **voll spielbare, strategische Pokemon-Battle-App für Android-Geräte**, die technisch sauber, modular und erweiterbar aufgebaut ist, eine ansprechende Grafik und ein einfaches, aber funktionales UI (auch für Touchscreens) bietet, und als Prototyp für zukünftige Erweiterungen (PvP, Kampfarenen, Team-Management) dienen kann .

Plan:

Phase 1: Planung & Design (19 Tage)

    Vollständiges Game Design Document erstellen

    UI/UX-Mockups für alle Kampf-Bildschirme

    Technische Architektur definieren

    Detailliertes Regelsystem ausarbeiten

Phase 2: Datensammlung (12 Tage)

    PokeAPI-Integration implementieren

    Sprite-Downloads automatisieren

    JSON-Datenbank strukturieren

    Asset-Pipeline einrichten

Phase 3: Frontend-Entwicklung (20 Tage)

    HTML5 Canvas Rendering-System

    Responsive CSS für alle Bildschirmgrößen

    JavaScript-Framework für Spiellogik

    Menüsystem und Navigation

Phase 4: Kampfsystem (23 Tage)

    Schadensberechnung implementieren

    KI-Verhalten programmieren

    Rundenlogik erstellen

    Status-Effekte einbauen

Phase 5: Integration & Testing (14 Tage)

    Bug-Fixes und Balancing

    Performance-Optimierung

    Browser-Kompatibilität testen

Phase 6: Mobile Deployment (7 Tage)

    Capacitor-Integration

    Android Studio Build

    APK-Erstellung und Testing
