<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    <div id="battleUI">
        <div id="playerHP"></div>
        <div id="enemyHP"></div>
        <div id="actionButtons"></div>
    </div>
    <script src="pokemon-data.js"></script>
    <script src="battle-system.js"></script>
    <script src="main.js"></script>
</body>
</html>

Javascript:

class Pokemon {
constructor(data) {
this.id = data.id;
this.name = data.name;
this.types = data.types;
this.stats = data.baseStats;
this.currentHP = this.stats.hp;
this.moves = data.moves;
this.level = 50; // Standard-Level für Kämpfe
}

    calculateDamage(move, target) {
        // Implementierung der Schadensformel
        const basePower = move.power;
        const attackStat = move.damageClass === 'physical' ? this.stats.attack : this.stats.specialAttack;
        const defenseStat = move.damageClass === 'physical' ? target.stats.defense : target.stats.specialDefense;

        let damage = ((this.level * 2 / 5 + 2) * basePower * attackStat / defenseStat / 50 + 2);

        // Typ-Effektivität anwenden
        const effectiveness = getTypeEffectiveness(move.type, target.types);
        damage *= effectiveness;

        // STAB anwenden
        if (this.types.includes(move.type)) {
            damage *= 1.5;
        }

        // Zufallsfaktor
        damage *= (Math.random() * 0.15 + 0.85);

        return Math.floor(damage);
    }

}

class BattleSystem {
constructor() {
this.playerPokemon = null;
this.enemyPokemon = null;
this.turnQueue = [];
this.gameState = 'selectAction';
}

    startBattle(playerPokemon, enemyPokemon) {
        this.playerPokemon = new Pokemon(playerPokemon);
        this.enemyPokemon = new Pokemon(enemyPokemon);
        this.updateUI();
    }

    executeAttack(attacker, defender, move) {
        const damage = attacker.calculateDamage(move, defender);
        defender.currentHP = Math.max(0, defender.currentHP - damage);

        if (defender.currentHP === 0) {
            this.handlePokemonFainted(defender);
        }
    }

}

Mobile / Responsive Design:
.battle-container {
display: grid;
grid-template-rows: 1fr auto;
height: 100vh;
max-width: 400px;
margin: 0 auto;
}

.pokemon-area {
position: relative;
background: linear-gradient(to bottom, #87CEEB 0%, #90EE90 100%);
}

.action-menu {
background: #2C3E50;
padding: 1rem;
display: grid;
grid-template-columns: 1fr 1fr;
gap: 0.5rem;
}

.action-button {
background: #3498DB;
color: white;
border: none;
padding: 1rem;
border-radius: 8px;
font-size: 1.1rem;
touch-action: manipulation;
}
