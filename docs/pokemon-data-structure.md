Pokemon-Datenstruktur

Jedes Pokemon benötigt folgende Grunddaten in json-Format für das Kampfsystem, hier ein Beispiel:

{
"id": 1,
"name": "Bulbasaur",
"types": ["Grass", "Poison"],
"baseStats": {
"hp": 45,
"attack": 49,
"defense": 49,
"specialAttack": 65,
"specialDefense": 65,
"speed": 45
},
"moves": [
{
"name": "Tackle",
"power": 40,
"accuracy": 100,
"pp": 35,
"type": "Normal",
"damageClass": "physical"
}
],
"sprites": {
"front": "url_to_front_sprite.png",
"back": "url_to_back_sprite.png"
}
}
