KI-System für NPC-Gegner
KI-Stufen

Basis-KI (Anfänger-Trainer):

    Zufällige Attackenauswahl

    Keine Typ-Vorteile berücksichtigt

    Wechselt Pokemon nur bei KO

Standard-KI (Durchschnittliche Trainer):

    Be<PERSON><PERSON>gt typ-effektive Attacken

    Grundlegende Schadens-Berechnung

    Gelegentliche strategische Pokemon-Wechsel

Experten-KI (Arenaleiter/Champ):

    Kennt exakte KP-Werte

    Nutzt Status-Attacken strategisch

    Intelligente Pokemon-Wechsel bei Typ-Nachteilen

    Berücksichtigt Prioritäts-Attacken
