Detaillierte Datenquellen für Pokemon-Informationen
Primäre API-Quelle: PokeAPI

PokeAPI (https://pokeapi.co) ist die umfassendste und zuverlässigste Quelle für Pokemon-Daten. Sie benötigen folgende Endpoints:

    GET https://pokeapi.co/api/v2/pokemon/?limit=151 - Liste aller 151 Gen-1 Pokemon

    GET https://pokeapi.co/api/v2/pokemon/{id} - Detaillierte Daten pro Pokemon

    GET https://pokeapi.co/api/v2/move/{id} - Attacken-Informationen

    GET https://pokeapi.co/api/v2/type/{id} - Typ-Effektivitäts-Daten

Bildmaterial-Quellen

Pokemon-Sprites:

    GitHub PokeAPI/sprites: Offizielle Repository mit allen Pokemon-Sprites in verschiedenen Generationen

    Pokemon Database: Hochqualitative Sprites für alle Generationen

    Bulbagarden Archives: Umfangreichste Sammlung mit verschiedenen Sprite-Versionen

Trainer-Bilder:

    Bulbagarden Archives: Komplette Sammlung aller NPC-Trainer-Sprites

    Veekun Sprite Archive: Alternative Quelle mit Generation-spezifischen Trainern

    Reddit Community Archives: Fangemachte HD-Versionen der klassischen Trainer
