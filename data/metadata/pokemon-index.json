[{"id": 1, "name": "bulbasaur", "filename": "001-bulbasaur.json", "types": ["grass", "poison"]}, {"id": 2, "name": "ivysaur", "filename": "002-ivysaur.json", "types": ["grass", "poison"]}, {"id": 3, "name": "venusaur", "filename": "003-venusaur.json", "types": ["grass", "poison"]}, {"id": 4, "name": "charmander", "filename": "004-charmander.json", "types": ["fire"]}, {"id": 5, "name": "charmeleon", "filename": "005-charmeleon.json", "types": ["fire"]}, {"id": 6, "name": "charizard", "filename": "006-charizard.json", "types": ["fire", "flying"]}, {"id": 7, "name": "squirtle", "filename": "007-squirtle.json", "types": ["water"]}, {"id": 8, "name": "wartortle", "filename": "008-wartortle.json", "types": ["water"]}, {"id": 9, "name": "blastoise", "filename": "009-blastoise.json", "types": ["water"]}, {"id": 10, "name": "caterpie", "filename": "010-caterpie.json", "types": ["bug"]}, {"id": 11, "name": "metapod", "filename": "011-metapod.json", "types": ["bug"]}, {"id": 12, "name": "butterfree", "filename": "012-butterfree.json", "types": ["bug", "flying"]}, {"id": 13, "name": "weedle", "filename": "013-weedle.json", "types": ["bug", "poison"]}, {"id": 14, "name": "kakuna", "filename": "014-kakuna.json", "types": ["bug", "poison"]}, {"id": 15, "name": "beedrill", "filename": "015-beedrill.json", "types": ["bug", "poison"]}, {"id": 16, "name": "pidgey", "filename": "016-pidgey.json", "types": ["normal", "flying"]}, {"id": 17, "name": "pidgeotto", "filename": "017-pidgeotto.json", "types": ["normal", "flying"]}, {"id": 18, "name": "pidgeot", "filename": "018-pidgeot.json", "types": ["normal", "flying"]}, {"id": 19, "name": "rattata", "filename": "019-rattata.json", "types": ["normal"]}, {"id": 20, "name": "raticate", "filename": "020-raticate.json", "types": ["normal"]}, {"id": 21, "name": "spearow", "filename": "021-spearow.json", "types": ["normal", "flying"]}, {"id": 22, "name": "fearow", "filename": "022-fearow.json", "types": ["normal", "flying"]}, {"id": 23, "name": "ekans", "filename": "023-ekans.json", "types": ["poison"]}, {"id": 24, "name": "arbok", "filename": "024-arbok.json", "types": ["poison"]}, {"id": 25, "name": "pikachu", "filename": "025-pikachu.json", "types": ["electric"]}, {"id": 26, "name": "r<PERSON><PERSON>", "filename": "026-raichu.json", "types": ["electric"]}, {"id": 27, "name": "sandshrew", "filename": "027-sandshrew.json", "types": ["ground"]}, {"id": 28, "name": "sandslash", "filename": "028-sandslash.json", "types": ["ground"]}, {"id": 29, "name": "nidoran-f", "filename": "029-ni<PERSON>an-f.json", "types": ["poison"]}, {"id": 30, "name": "nidorina", "filename": "030-nidorina.json", "types": ["poison"]}, {"id": 31, "name": "nidoqueen", "filename": "031-nidoqueen.json", "types": ["poison", "ground"]}, {"id": 32, "name": "nidoran-m", "filename": "032-ni<PERSON><PERSON>-m.json", "types": ["poison"]}, {"id": 33, "name": "<PERSON><PERSON><PERSON>", "filename": "033-nidorino.json", "types": ["poison"]}, {"id": 34, "name": "nidoking", "filename": "034-nidoking.json", "types": ["poison", "ground"]}, {"id": 35, "name": "cle<PERSON>y", "filename": "035-cle<PERSON>y.json", "types": ["fairy"]}, {"id": 36, "name": "clefable", "filename": "036-clefable.json", "types": ["fairy"]}, {"id": 37, "name": "vulpix", "filename": "037-vulpix.json", "types": ["fire"]}, {"id": 38, "name": "ninetales", "filename": "038-ninetales.json", "types": ["fire"]}, {"id": 39, "name": "jigglypuff", "filename": "039-jigglypuff.json", "types": ["normal", "fairy"]}, {"id": 40, "name": "wigglytuff", "filename": "040-wigglytuff.json", "types": ["normal", "fairy"]}, {"id": 41, "name": "zubat", "filename": "041-zubat.json", "types": ["poison", "flying"]}, {"id": 42, "name": "golbat", "filename": "042-golbat.json", "types": ["poison", "flying"]}, {"id": 43, "name": "oddish", "filename": "043-oddish.json", "types": ["grass", "poison"]}, {"id": 44, "name": "gloom", "filename": "044-gloom.json", "types": ["grass", "poison"]}, {"id": 45, "name": "vileplume", "filename": "045-vileplume.json", "types": ["grass", "poison"]}, {"id": 46, "name": "paras", "filename": "046-paras.json", "types": ["bug", "grass"]}, {"id": 47, "name": "parasect", "filename": "047-parasect.json", "types": ["bug", "grass"]}, {"id": 48, "name": "venonat", "filename": "048-venonat.json", "types": ["bug", "poison"]}, {"id": 49, "name": "venomoth", "filename": "049-venomoth.json", "types": ["bug", "poison"]}, {"id": 50, "name": "<PERSON><PERSON>", "filename": "050-diglett.json", "types": ["ground"]}, {"id": 51, "name": "<PERSON><PERSON>o", "filename": "051-dugtrio.json", "types": ["ground"]}, {"id": 52, "name": "meowth", "filename": "052-meowth.json", "types": ["normal"]}, {"id": 53, "name": "persian", "filename": "053-persian.json", "types": ["normal"]}, {"id": 54, "name": "psyduck", "filename": "054-psyduck.json", "types": ["water"]}, {"id": 55, "name": "golduck", "filename": "055-golduck.json", "types": ["water"]}, {"id": 56, "name": "mankey", "filename": "056-mankey.json", "types": ["fighting"]}, {"id": 57, "name": "primeape", "filename": "057-primeape.json", "types": ["fighting"]}, {"id": 58, "name": "growlithe", "filename": "058-growlithe.json", "types": ["fire"]}]