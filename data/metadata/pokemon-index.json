[{"id": 1, "name": "bulbasaur", "filename": "001-bulbasaur.json", "types": ["grass", "poison"]}, {"id": 2, "name": "ivysaur", "filename": "002-ivysaur.json", "types": ["grass", "poison"]}, {"id": 3, "name": "venusaur", "filename": "003-venusaur.json", "types": ["grass", "poison"]}, {"id": 4, "name": "charmander", "filename": "004-charmander.json", "types": ["fire"]}, {"id": 5, "name": "charmeleon", "filename": "005-charmeleon.json", "types": ["fire"]}, {"id": 6, "name": "charizard", "filename": "006-charizard.json", "types": ["fire", "flying"]}, {"id": 7, "name": "squirtle", "filename": "007-squirtle.json", "types": ["water"]}, {"id": 8, "name": "wartortle", "filename": "008-wartortle.json", "types": ["water"]}, {"id": 9, "name": "blastoise", "filename": "009-blastoise.json", "types": ["water"]}, {"id": 10, "name": "caterpie", "filename": "010-caterpie.json", "types": ["bug"]}, {"id": 11, "name": "metapod", "filename": "011-metapod.json", "types": ["bug"]}, {"id": 12, "name": "butterfree", "filename": "012-butterfree.json", "types": ["bug", "flying"]}, {"id": 13, "name": "weedle", "filename": "013-weedle.json", "types": ["bug", "poison"]}, {"id": 14, "name": "kakuna", "filename": "014-kakuna.json", "types": ["bug", "poison"]}, {"id": 15, "name": "beedrill", "filename": "015-beedrill.json", "types": ["bug", "poison"]}, {"id": 16, "name": "pidgey", "filename": "016-pidgey.json", "types": ["normal", "flying"]}, {"id": 17, "name": "pidgeotto", "filename": "017-pidgeotto.json", "types": ["normal", "flying"]}, {"id": 18, "name": "pidgeot", "filename": "018-pidgeot.json", "types": ["normal", "flying"]}, {"id": 19, "name": "rattata", "filename": "019-rattata.json", "types": ["normal"]}, {"id": 20, "name": "raticate", "filename": "020-raticate.json", "types": ["normal"]}, {"id": 21, "name": "spearow", "filename": "021-spearow.json", "types": ["normal", "flying"]}, {"id": 22, "name": "fearow", "filename": "022-fearow.json", "types": ["normal", "flying"]}, {"id": 23, "name": "ekans", "filename": "023-ekans.json", "types": ["poison"]}, {"id": 24, "name": "arbok", "filename": "024-arbok.json", "types": ["poison"]}, {"id": 25, "name": "pikachu", "filename": "025-pikachu.json", "types": ["electric"]}, {"id": 26, "name": "r<PERSON><PERSON>", "filename": "026-raichu.json", "types": ["electric"]}, {"id": 27, "name": "sandshrew", "filename": "027-sandshrew.json", "types": ["ground"]}, {"id": 28, "name": "sandslash", "filename": "028-sandslash.json", "types": ["ground"]}, {"id": 29, "name": "nidoran-f", "filename": "029-ni<PERSON>an-f.json", "types": ["poison"]}, {"id": 30, "name": "nidorina", "filename": "030-nidorina.json", "types": ["poison"]}, {"id": 31, "name": "nidoqueen", "filename": "031-nidoqueen.json", "types": ["poison", "ground"]}, {"id": 32, "name": "nidoran-m", "filename": "032-ni<PERSON><PERSON>-m.json", "types": ["poison"]}, {"id": 33, "name": "<PERSON><PERSON><PERSON>", "filename": "033-nidorino.json", "types": ["poison"]}, {"id": 34, "name": "nidoking", "filename": "034-nidoking.json", "types": ["poison", "ground"]}, {"id": 35, "name": "cle<PERSON>y", "filename": "035-cle<PERSON>y.json", "types": ["fairy"]}, {"id": 36, "name": "clefable", "filename": "036-clefable.json", "types": ["fairy"]}, {"id": 37, "name": "vulpix", "filename": "037-vulpix.json", "types": ["fire"]}, {"id": 38, "name": "ninetales", "filename": "038-ninetales.json", "types": ["fire"]}, {"id": 39, "name": "jigglypuff", "filename": "039-jigglypuff.json", "types": ["normal", "fairy"]}, {"id": 40, "name": "wigglytuff", "filename": "040-wigglytuff.json", "types": ["normal", "fairy"]}, {"id": 41, "name": "zubat", "filename": "041-zubat.json", "types": ["poison", "flying"]}, {"id": 42, "name": "golbat", "filename": "042-golbat.json", "types": ["poison", "flying"]}, {"id": 43, "name": "oddish", "filename": "043-oddish.json", "types": ["grass", "poison"]}, {"id": 44, "name": "gloom", "filename": "044-gloom.json", "types": ["grass", "poison"]}, {"id": 45, "name": "vileplume", "filename": "045-vileplume.json", "types": ["grass", "poison"]}, {"id": 46, "name": "paras", "filename": "046-paras.json", "types": ["bug", "grass"]}, {"id": 47, "name": "parasect", "filename": "047-parasect.json", "types": ["bug", "grass"]}, {"id": 48, "name": "venonat", "filename": "048-venonat.json", "types": ["bug", "poison"]}, {"id": 49, "name": "venomoth", "filename": "049-venomoth.json", "types": ["bug", "poison"]}, {"id": 50, "name": "<PERSON><PERSON>", "filename": "050-diglett.json", "types": ["ground"]}, {"id": 51, "name": "<PERSON><PERSON>o", "filename": "051-dugtrio.json", "types": ["ground"]}, {"id": 52, "name": "meowth", "filename": "052-meowth.json", "types": ["normal"]}, {"id": 53, "name": "persian", "filename": "053-persian.json", "types": ["normal"]}, {"id": 54, "name": "psyduck", "filename": "054-psyduck.json", "types": ["water"]}, {"id": 55, "name": "golduck", "filename": "055-golduck.json", "types": ["water"]}, {"id": 56, "name": "mankey", "filename": "056-mankey.json", "types": ["fighting"]}, {"id": 57, "name": "primeape", "filename": "057-primeape.json", "types": ["fighting"]}, {"id": 58, "name": "growlithe", "filename": "058-growlithe.json", "types": ["fire"]}, {"id": 59, "name": "arcanine", "filename": "059-arcanine.json", "types": ["fire"]}, {"id": 60, "name": "poliwag", "filename": "060-poliwag.json", "types": ["water"]}, {"id": 61, "name": "poliwhirl", "filename": "061-poliwhirl.json", "types": ["water"]}, {"id": 62, "name": "poliwrath", "filename": "062-poliwrath.json", "types": ["water", "fighting"]}, {"id": 63, "name": "abra", "filename": "063-abra.json", "types": ["psychic"]}, {"id": 64, "name": "kadabra", "filename": "064-kadabra.json", "types": ["psychic"]}, {"id": 65, "name": "<PERSON><PERSON><PERSON>", "filename": "065-alakazam.json", "types": ["psychic"]}, {"id": 66, "name": "machop", "filename": "066-machop.json", "types": ["fighting"]}, {"id": 67, "name": "machoke", "filename": "067-machoke.json", "types": ["fighting"]}, {"id": 68, "name": "machamp", "filename": "068-machamp.json", "types": ["fighting"]}, {"id": 69, "name": "bellsprout", "filename": "069-bellsprout.json", "types": ["grass", "poison"]}, {"id": 70, "name": "weepinbell", "filename": "070-weepinbell.json", "types": ["grass", "poison"]}, {"id": 71, "name": "victreebel", "filename": "071-victreebel.json", "types": ["grass", "poison"]}, {"id": 72, "name": "tentacool", "filename": "072-tentacool.json", "types": ["water", "poison"]}, {"id": 73, "name": "tentacruel", "filename": "073-tentacruel.json", "types": ["water", "poison"]}, {"id": 74, "name": "geodude", "filename": "074-geodude.json", "types": ["rock", "ground"]}, {"id": 75, "name": "graveler", "filename": "075-graveler.json", "types": ["rock", "ground"]}, {"id": 76, "name": "golem", "filename": "076-golem.json", "types": ["rock", "ground"]}, {"id": 77, "name": "ponyta", "filename": "077-ponyta.json", "types": ["fire"]}, {"id": 78, "name": "rapidash", "filename": "078-rapidash.json", "types": ["fire"]}, {"id": 79, "name": "slowpoke", "filename": "079-slowpoke.json", "types": ["water", "psychic"]}, {"id": 80, "name": "slowbro", "filename": "080-slowbro.json", "types": ["water", "psychic"]}, {"id": 81, "name": "magnemite", "filename": "081-magnemite.json", "types": ["electric", "steel"]}, {"id": 82, "name": "magneton", "filename": "082-magneton.json", "types": ["electric", "steel"]}, {"id": 83, "name": "farfetchd", "filename": "083-farfetchd.json", "types": ["normal", "flying"]}, {"id": 84, "name": "doduo", "filename": "084-doduo.json", "types": ["normal", "flying"]}, {"id": 85, "name": "dodrio", "filename": "085-dodrio.json", "types": ["normal", "flying"]}, {"id": 86, "name": "seel", "filename": "086-seel.json", "types": ["water"]}, {"id": 87, "name": "dewgong", "filename": "087-dewgong.json", "types": ["water", "ice"]}, {"id": 88, "name": "grimer", "filename": "088-grimer.json", "types": ["poison"]}, {"id": 89, "name": "muk", "filename": "089-muk.json", "types": ["poison"]}, {"id": 90, "name": "shellder", "filename": "090-shellder.json", "types": ["water"]}, {"id": 91, "name": "cloyster", "filename": "091-cloyster.json", "types": ["water", "ice"]}, {"id": 92, "name": "gastly", "filename": "092-gastly.json", "types": ["ghost", "poison"]}, {"id": 93, "name": "haunter", "filename": "093-haunter.json", "types": ["ghost", "poison"]}, {"id": 94, "name": "gengar", "filename": "094-gengar.json", "types": ["ghost", "poison"]}, {"id": 95, "name": "onix", "filename": "095-onix.json", "types": ["rock", "ground"]}, {"id": 96, "name": "drowzee", "filename": "096-drow<PERSON>.json", "types": ["psychic"]}, {"id": 97, "name": "hypno", "filename": "097-hypno.json", "types": ["psychic"]}, {"id": 98, "name": "krabby", "filename": "098-krabby.json", "types": ["water"]}, {"id": 99, "name": "kingler", "filename": "099-kingler.json", "types": ["water"]}, {"id": 100, "name": "voltorb", "filename": "100-voltorb.json", "types": ["electric"]}, {"id": 101, "name": "electrode", "filename": "101-electrode.json", "types": ["electric"]}, {"id": 102, "name": "exeggcute", "filename": "102-exeggcute.json", "types": ["grass", "psychic"]}, {"id": 103, "name": "exeggutor", "filename": "103-exeggutor.json", "types": ["grass", "psychic"]}, {"id": 104, "name": "cubone", "filename": "104-cu<PERSON>.json", "types": ["ground"]}, {"id": 105, "name": "marowak", "filename": "105-marowak.json", "types": ["ground"]}, {"id": 106, "name": "<PERSON><PERSON><PERSON>", "filename": "106-hitmonlee.json", "types": ["fighting"]}, {"id": 107, "name": "hitmonchan", "filename": "107-hitmonchan.json", "types": ["fighting"]}, {"id": 108, "name": "lickitung", "filename": "108-lickitung.json", "types": ["normal"]}, {"id": 109, "name": "koffing", "filename": "109-koffing.json", "types": ["poison"]}, {"id": 110, "name": "weezing", "filename": "110-weezing.json", "types": ["poison"]}, {"id": 111, "name": "rhyhorn", "filename": "111-rhy<PERSON>.json", "types": ["ground", "rock"]}, {"id": 112, "name": "rhydon", "filename": "112-rhydon.json", "types": ["ground", "rock"]}, {"id": 113, "name": "chansey", "filename": "113-chansey.json", "types": ["normal"]}, {"id": 114, "name": "tangela", "filename": "114-tangela.json", "types": ["grass"]}, {"id": 115, "name": "kangaskhan", "filename": "115-kangaskhan.json", "types": ["normal"]}, {"id": 116, "name": "<PERSON>a", "filename": "116-horsea.json", "types": ["water"]}, {"id": 117, "name": "seadra", "filename": "117-seadra.json", "types": ["water"]}, {"id": 118, "name": "goldeen", "filename": "118-gold<PERSON>.json", "types": ["water"]}, {"id": 119, "name": "seaking", "filename": "119-seaking.json", "types": ["water"]}, {"id": 120, "name": "staryu", "filename": "120-staryu.json", "types": ["water"]}, {"id": 121, "name": "starmie", "filename": "121-star<PERSON>.json", "types": ["water", "psychic"]}, {"id": 122, "name": "mr-mime", "filename": "122-mr-mime.json", "types": ["psychic", "fairy"]}, {"id": 123, "name": "scyther", "filename": "123-scyther.json", "types": ["bug", "flying"]}, {"id": 124, "name": "jynx", "filename": "124-jynx.json", "types": ["ice", "psychic"]}, {"id": 125, "name": "electabuzz", "filename": "125-electabuzz.json", "types": ["electric"]}, {"id": 126, "name": "magmar", "filename": "126-magmar.json", "types": ["fire"]}, {"id": 127, "name": "pinsir", "filename": "127-pins<PERSON>.json", "types": ["bug"]}, {"id": 128, "name": "tauros", "filename": "128-tauros.json", "types": ["normal"]}, {"id": 129, "name": "magi<PERSON><PERSON>", "filename": "129-magikarp.json", "types": ["water"]}, {"id": 130, "name": "gyarados", "filename": "130-gyarados.json", "types": ["water", "flying"]}, {"id": 131, "name": "<PERSON>ras", "filename": "131-lapras.json", "types": ["water", "ice"]}, {"id": 132, "name": "ditto", "filename": "132-ditto.json", "types": ["normal"]}, {"id": 133, "name": "eevee", "filename": "133-eevee.json", "types": ["normal"]}, {"id": 134, "name": "vaporeon", "filename": "134-vaporeon.json", "types": ["water"]}, {"id": 135, "name": "jolteon", "filename": "135-jolt<PERSON>.json", "types": ["electric"]}, {"id": 136, "name": "flareon", "filename": "136-<PERSON>on.json", "types": ["fire"]}, {"id": 137, "name": "porygon", "filename": "137-porygon.json", "types": ["normal"]}, {"id": 138, "name": "omanyte", "filename": "138-omanyte.json", "types": ["rock", "water"]}, {"id": 139, "name": "omastar", "filename": "139-omastar.json", "types": ["rock", "water"]}, {"id": 140, "name": "kabuto", "filename": "140-kabuto.json", "types": ["rock", "water"]}, {"id": 141, "name": "kabutops", "filename": "141-kabutops.json", "types": ["rock", "water"]}, {"id": 142, "name": "aerodactyl", "filename": "142-aerodactyl.json", "types": ["rock", "flying"]}, {"id": 143, "name": "snorlax", "filename": "143-snor<PERSON>.json", "types": ["normal"]}, {"id": 144, "name": "articuno", "filename": "144-articuno.json", "types": ["ice", "flying"]}, {"id": 145, "name": "zapdos", "filename": "145-zapdos.json", "types": ["electric", "flying"]}, {"id": 146, "name": "moltres", "filename": "146-moltres.json", "types": ["fire", "flying"]}, {"id": 147, "name": "dratini", "filename": "147-drat<PERSON>.json", "types": ["dragon"]}, {"id": 148, "name": "dragonair", "filename": "148-dragonair.json", "types": ["dragon"]}, {"id": 149, "name": "dragonite", "filename": "149-dragonite.json", "types": ["dragon", "flying"]}, {"id": 150, "name": "mewtwo", "filename": "150-mewtwo.json", "types": ["psychic"]}, {"id": 151, "name": "mew", "filename": "151-mew.json", "types": ["psychic"]}]