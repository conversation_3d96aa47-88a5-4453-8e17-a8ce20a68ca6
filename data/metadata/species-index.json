[{"id": 1, "name": "bulbasaur", "filename": "001-bulbasaur-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/1/"}, {"id": 2, "name": "ivysaur", "filename": "002-ivysaur-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/1/"}, {"id": 3, "name": "venusaur", "filename": "003-venusaur-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/1/"}, {"id": 4, "name": "charmander", "filename": "004-charmander-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/2/"}, {"id": 5, "name": "charmeleon", "filename": "005-charmeleon-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/2/"}, {"id": 6, "name": "charizard", "filename": "006-charizard-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/2/"}, {"id": 7, "name": "squirtle", "filename": "007-squirtle-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/3/"}, {"id": 8, "name": "wartortle", "filename": "008-wartortle-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/3/"}, {"id": 9, "name": "blastoise", "filename": "009-blastoise-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/3/"}, {"id": 10, "name": "caterpie", "filename": "010-caterpie-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/4/"}, {"id": 11, "name": "metapod", "filename": "011-metapod-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/4/"}, {"id": 12, "name": "butterfree", "filename": "012-butterfree-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/4/"}, {"id": 13, "name": "weedle", "filename": "013-weedle-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/5/"}, {"id": 14, "name": "kakuna", "filename": "014-kakuna-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/5/"}, {"id": 15, "name": "beedrill", "filename": "015-beedrill-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/5/"}, {"id": 16, "name": "pidgey", "filename": "016-pidgey-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/6/"}, {"id": 17, "name": "pidgeotto", "filename": "017-pidgeotto-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/6/"}, {"id": 18, "name": "pidgeot", "filename": "018-pidgeot-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/6/"}, {"id": 19, "name": "rattata", "filename": "019-rattata-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/7/"}, {"id": 20, "name": "raticate", "filename": "020-raticate-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/7/"}, {"id": 21, "name": "spearow", "filename": "021-spearow-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/8/"}, {"id": 22, "name": "fearow", "filename": "022-fearow-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/8/"}, {"id": 23, "name": "ekans", "filename": "023-ekans-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/9/"}, {"id": 24, "name": "arbok", "filename": "024-arbok-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/9/"}, {"id": 25, "name": "pikachu", "filename": "025-pikachu-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/10/"}, {"id": 26, "name": "r<PERSON><PERSON>", "filename": "026-raichu-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/10/"}, {"id": 27, "name": "sandshrew", "filename": "027-sandshrew-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/11/"}, {"id": 28, "name": "sandslash", "filename": "028-sandslash-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/11/"}, {"id": 29, "name": "nidoran-f", "filename": "029-nidoran-f-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/12/"}, {"id": 30, "name": "nidorina", "filename": "030-nidorina-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/12/"}, {"id": 31, "name": "nidoqueen", "filename": "031-nidoqueen-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/12/"}, {"id": 32, "name": "nidoran-m", "filename": "032-nidoran-m-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/13/"}, {"id": 33, "name": "<PERSON><PERSON><PERSON>", "filename": "033-nidorino-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/13/"}, {"id": 34, "name": "nidoking", "filename": "034-nidoking-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/13/"}, {"id": 35, "name": "cle<PERSON>y", "filename": "035-clefairy-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/14/"}, {"id": 36, "name": "clefable", "filename": "036-clefable-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/14/"}, {"id": 37, "name": "vulpix", "filename": "037-vulpix-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/15/"}, {"id": 38, "name": "ninetales", "filename": "038-ninetales-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/15/"}, {"id": 39, "name": "jigglypuff", "filename": "039-jigglypuff-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/16/"}, {"id": 40, "name": "wigglytuff", "filename": "040-wigglytuff-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/16/"}, {"id": 41, "name": "zubat", "filename": "041-zubat-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/17/"}, {"id": 42, "name": "golbat", "filename": "042-golbat-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/17/"}, {"id": 43, "name": "oddish", "filename": "043-oddish-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/18/"}, {"id": 44, "name": "gloom", "filename": "044-gloom-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/18/"}, {"id": 45, "name": "vileplume", "filename": "045-vileplume-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/18/"}, {"id": 46, "name": "paras", "filename": "046-paras-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/19/"}, {"id": 47, "name": "parasect", "filename": "047-parasect-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/19/"}, {"id": 48, "name": "venonat", "filename": "048-venonat-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/20/"}, {"id": 49, "name": "venomoth", "filename": "049-venomoth-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/20/"}, {"id": 50, "name": "<PERSON><PERSON>", "filename": "050-diglett-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/21/"}, {"id": 51, "name": "<PERSON><PERSON>o", "filename": "051-dugtrio-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/21/"}, {"id": 52, "name": "meowth", "filename": "052-meowth-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/22/"}, {"id": 53, "name": "persian", "filename": "053-persian-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/22/"}, {"id": 54, "name": "psyduck", "filename": "054-psyduck-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/23/"}, {"id": 55, "name": "golduck", "filename": "055-golduck-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/23/"}, {"id": 56, "name": "mankey", "filename": "056-mankey-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/24/"}, {"id": 57, "name": "primeape", "filename": "057-primeape-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/24/"}, {"id": 58, "name": "growlithe", "filename": "058-growlithe-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/25/"}, {"id": 59, "name": "arcanine", "filename": "059-arcanine-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/25/"}, {"id": 60, "name": "poliwag", "filename": "060-poliwag-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/26/"}, {"id": 61, "name": "poliwhirl", "filename": "061-poliwhirl-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/26/"}, {"id": 62, "name": "poliwrath", "filename": "062-poliwrath-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/26/"}, {"id": 63, "name": "abra", "filename": "063-abra-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/27/"}, {"id": 64, "name": "kadabra", "filename": "064-kadabra-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/27/"}, {"id": 65, "name": "<PERSON><PERSON><PERSON>", "filename": "065-alaka<PERSON>-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/27/"}, {"id": 66, "name": "machop", "filename": "066-machop-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/28/"}, {"id": 67, "name": "machoke", "filename": "067-machoke-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/28/"}, {"id": 68, "name": "machamp", "filename": "068-machamp-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/28/"}, {"id": 69, "name": "bellsprout", "filename": "069-bellsprout-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/29/"}, {"id": 70, "name": "weepinbell", "filename": "070-weep<PERSON>bell-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/29/"}, {"id": 71, "name": "victreebel", "filename": "071-victreebel-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/29/"}, {"id": 72, "name": "tentacool", "filename": "072-tentacool-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/30/"}, {"id": 73, "name": "tentacruel", "filename": "073-tentacruel-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/30/"}, {"id": 74, "name": "geodude", "filename": "074-geodude-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/31/"}, {"id": 75, "name": "graveler", "filename": "075-graveler-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/31/"}, {"id": 76, "name": "golem", "filename": "076-golem-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/31/"}, {"id": 77, "name": "ponyta", "filename": "077-ponyta-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/32/"}, {"id": 78, "name": "rapidash", "filename": "078-rapidash-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/32/"}, {"id": 79, "name": "slowpoke", "filename": "079-slowpoke-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/33/"}, {"id": 80, "name": "slowbro", "filename": "080-slowbro-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/33/"}, {"id": 81, "name": "magnemite", "filename": "081-magnemite-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/34/"}, {"id": 82, "name": "magneton", "filename": "082-magneton-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/34/"}, {"id": 83, "name": "farfetchd", "filename": "083-farfetchd-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/35/"}, {"id": 84, "name": "doduo", "filename": "084-doduo-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/36/"}, {"id": 85, "name": "dodrio", "filename": "085-dodrio-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/36/"}, {"id": 86, "name": "seel", "filename": "086-seel-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/37/"}, {"id": 87, "name": "dewgong", "filename": "087-dewgong-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/37/"}, {"id": 88, "name": "grimer", "filename": "088-grimer-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/38/"}, {"id": 89, "name": "muk", "filename": "089-muk-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/38/"}, {"id": 90, "name": "shellder", "filename": "090-shellder-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/39/"}, {"id": 91, "name": "cloyster", "filename": "091-cloyster-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/39/"}, {"id": 92, "name": "gastly", "filename": "092-gastly-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/40/"}, {"id": 93, "name": "haunter", "filename": "093-haunter-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/40/"}, {"id": 94, "name": "gengar", "filename": "094-gengar-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/40/"}, {"id": 95, "name": "onix", "filename": "095-onix-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/41/"}, {"id": 96, "name": "drowzee", "filename": "096-drowzee-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/42/"}, {"id": 97, "name": "hypno", "filename": "097-hypno-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/42/"}, {"id": 98, "name": "krabby", "filename": "098-krabby-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/43/"}, {"id": 99, "name": "kingler", "filename": "099-kingler-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/43/"}, {"id": 100, "name": "voltorb", "filename": "100-voltorb-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/44/"}, {"id": 101, "name": "electrode", "filename": "101-electrode-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/44/"}, {"id": 102, "name": "exeggcute", "filename": "102-exeggcute-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/45/"}, {"id": 103, "name": "exeggutor", "filename": "103-exeggutor-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/45/"}, {"id": 104, "name": "cubone", "filename": "104-cubone-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/46/"}, {"id": 105, "name": "marowak", "filename": "105-marowak-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/46/"}, {"id": 106, "name": "<PERSON><PERSON><PERSON>", "filename": "106-hit<PERSON>lee-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/47/"}, {"id": 107, "name": "hitmonchan", "filename": "107-hitmonchan-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/47/"}, {"id": 108, "name": "lickitung", "filename": "108-lickitung-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/48/"}, {"id": 109, "name": "koffing", "filename": "109-koffing-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/49/"}, {"id": 110, "name": "weezing", "filename": "110-weezing-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/49/"}, {"id": 111, "name": "rhyhorn", "filename": "111-rhyhorn-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/50/"}, {"id": 112, "name": "rhydon", "filename": "112-rhydon-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/50/"}, {"id": 113, "name": "chansey", "filename": "113-chansey-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/51/"}, {"id": 114, "name": "tangela", "filename": "114-tangela-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/52/"}, {"id": 115, "name": "kangaskhan", "filename": "115-kangaskhan-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/53/"}, {"id": 116, "name": "<PERSON>a", "filename": "116-horsea-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/54/"}, {"id": 117, "name": "seadra", "filename": "117-seadra-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/54/"}, {"id": 118, "name": "goldeen", "filename": "118-goldeen-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/55/"}, {"id": 119, "name": "seaking", "filename": "119-seaking-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/55/"}, {"id": 120, "name": "staryu", "filename": "120-staryu-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/56/"}, {"id": 121, "name": "starmie", "filename": "121-starmie-<PERSON>.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/56/"}, {"id": 122, "name": "mr-mime", "filename": "122-mr-mime-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/57/"}, {"id": 123, "name": "scyther", "filename": "123-scyther-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/58/"}, {"id": 124, "name": "jynx", "filename": "124-jynx-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/59/"}, {"id": 125, "name": "electabuzz", "filename": "125-electabuzz-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/60/"}, {"id": 126, "name": "magmar", "filename": "126-magmar-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/61/"}, {"id": 127, "name": "pinsir", "filename": "127-pinsir-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/62/"}, {"id": 128, "name": "tauros", "filename": "128-tauros-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/63/"}, {"id": 129, "name": "magi<PERSON><PERSON>", "filename": "129-magikarp-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/64/"}, {"id": 130, "name": "gyarados", "filename": "130-gyarados-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/64/"}, {"id": 131, "name": "<PERSON>ras", "filename": "131-lapras-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/65/"}, {"id": 132, "name": "ditto", "filename": "132-ditto-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/66/"}, {"id": 133, "name": "eevee", "filename": "133-eevee-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/67/"}, {"id": 134, "name": "vaporeon", "filename": "134-vaporeon-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/67/"}, {"id": 135, "name": "jolteon", "filename": "135-jolteon-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/67/"}, {"id": 136, "name": "flareon", "filename": "136-flareon-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/67/"}, {"id": 137, "name": "porygon", "filename": "137-porygon-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/68/"}, {"id": 138, "name": "omanyte", "filename": "138-omanyte-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/69/"}, {"id": 139, "name": "omastar", "filename": "139-omastar-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/69/"}, {"id": 140, "name": "kabuto", "filename": "140-kabuto-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/70/"}, {"id": 141, "name": "kabutops", "filename": "141-kabutops-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/70/"}, {"id": 142, "name": "aerodactyl", "filename": "142-aerodactyl-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/71/"}, {"id": 143, "name": "snorlax", "filename": "143-snorlax-<PERSON>.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/72/"}, {"id": 144, "name": "articuno", "filename": "144-articuno-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/73/"}, {"id": 145, "name": "zapdos", "filename": "145-zapdos-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/74/"}, {"id": 146, "name": "moltres", "filename": "146-moltres-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/75/"}, {"id": 147, "name": "dratini", "filename": "147-dratini-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/76/"}, {"id": 148, "name": "dragonair", "filename": "148-dragonair-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/76/"}, {"id": 149, "name": "dragonite", "filename": "149-dragonite-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/76/"}, {"id": 150, "name": "mewtwo", "filename": "150-mewtwo-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/77/"}, {"id": 151, "name": "mew", "filename": "151-mew-species.json", "evolutionChainUrl": "https://pokeapi.co/api/v2/evolution-chain/78/"}]