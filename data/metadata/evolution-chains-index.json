[{"id": 1, "filename": "chain-1.json", "baseSpecies": "bulbasaur"}, {"id": 2, "filename": "chain-2.json", "baseSpecies": "charmander"}, {"id": 3, "filename": "chain-3.json", "baseSpecies": "squirtle"}, {"id": 4, "filename": "chain-4.json", "baseSpecies": "caterpie"}, {"id": 5, "filename": "chain-5.json", "baseSpecies": "weedle"}, {"id": 6, "filename": "chain-6.json", "baseSpecies": "pidgey"}, {"id": 7, "filename": "chain-7.json", "baseSpecies": "rattata"}, {"id": 8, "filename": "chain-8.json", "baseSpecies": "spearow"}, {"id": 9, "filename": "chain-9.json", "baseSpecies": "ekans"}, {"id": 10, "filename": "chain-10.json", "baseSpecies": "pichu"}, {"id": 11, "filename": "chain-11.json", "baseSpecies": "sandshrew"}, {"id": 12, "filename": "chain-12.json", "baseSpecies": "nidoran-f"}, {"id": 13, "filename": "chain-13.json", "baseSpecies": "nidoran-m"}, {"id": 14, "filename": "chain-14.json", "baseSpecies": "cleffa"}, {"id": 15, "filename": "chain-15.json", "baseSpecies": "vulpix"}, {"id": 16, "filename": "chain-16.json", "baseSpecies": "igglybuff"}, {"id": 17, "filename": "chain-17.json", "baseSpecies": "zubat"}, {"id": 18, "filename": "chain-18.json", "baseSpecies": "oddish"}, {"id": 19, "filename": "chain-19.json", "baseSpecies": "paras"}, {"id": 20, "filename": "chain-20.json", "baseSpecies": "venonat"}, {"id": 21, "filename": "chain-21.json", "baseSpecies": "<PERSON><PERSON>"}, {"id": 22, "filename": "chain-22.json", "baseSpecies": "meowth"}, {"id": 23, "filename": "chain-23.json", "baseSpecies": "psyduck"}, {"id": 24, "filename": "chain-24.json", "baseSpecies": "mankey"}, {"id": 25, "filename": "chain-25.json", "baseSpecies": "growlithe"}, {"id": 26, "filename": "chain-26.json", "baseSpecies": "poliwag"}, {"id": 27, "filename": "chain-27.json", "baseSpecies": "abra"}, {"id": 28, "filename": "chain-28.json", "baseSpecies": "machop"}, {"id": 29, "filename": "chain-29.json", "baseSpecies": "bellsprout"}, {"id": 30, "filename": "chain-30.json", "baseSpecies": "tentacool"}, {"id": 31, "filename": "chain-31.json", "baseSpecies": "geodude"}, {"id": 32, "filename": "chain-32.json", "baseSpecies": "ponyta"}, {"id": 33, "filename": "chain-33.json", "baseSpecies": "slowpoke"}, {"id": 34, "filename": "chain-34.json", "baseSpecies": "magnemite"}, {"id": 35, "filename": "chain-35.json", "baseSpecies": "farfetchd"}, {"id": 36, "filename": "chain-36.json", "baseSpecies": "doduo"}, {"id": 37, "filename": "chain-37.json", "baseSpecies": "seel"}, {"id": 38, "filename": "chain-38.json", "baseSpecies": "grimer"}, {"id": 39, "filename": "chain-39.json", "baseSpecies": "shellder"}, {"id": 40, "filename": "chain-40.json", "baseSpecies": "gastly"}, {"id": 41, "filename": "chain-41.json", "baseSpecies": "onix"}, {"id": 42, "filename": "chain-42.json", "baseSpecies": "drowzee"}, {"id": 43, "filename": "chain-43.json", "baseSpecies": "krabby"}, {"id": 44, "filename": "chain-44.json", "baseSpecies": "voltorb"}, {"id": 45, "filename": "chain-45.json", "baseSpecies": "exeggcute"}, {"id": 46, "filename": "chain-46.json", "baseSpecies": "cubone"}, {"id": 47, "filename": "chain-47.json", "baseSpecies": "tyrogue"}, {"id": 48, "filename": "chain-48.json", "baseSpecies": "lickitung"}, {"id": 49, "filename": "chain-49.json", "baseSpecies": "koffing"}, {"id": 50, "filename": "chain-50.json", "baseSpecies": "rhyhorn"}, {"id": 51, "filename": "chain-51.json", "baseSpecies": "happiny"}, {"id": 52, "filename": "chain-52.json", "baseSpecies": "tangela"}, {"id": 53, "filename": "chain-53.json", "baseSpecies": "kangaskhan"}, {"id": 54, "filename": "chain-54.json", "baseSpecies": "<PERSON>a"}, {"id": 55, "filename": "chain-55.json", "baseSpecies": "goldeen"}, {"id": 56, "filename": "chain-56.json", "baseSpecies": "staryu"}, {"id": 57, "filename": "chain-57.json", "baseSpecies": "mime-jr"}, {"id": 58, "filename": "chain-58.json", "baseSpecies": "scyther"}, {"id": 59, "filename": "chain-59.json", "baseSpecies": "smoochum"}, {"id": 60, "filename": "chain-60.json", "baseSpecies": "elekid"}, {"id": 61, "filename": "chain-61.json", "baseSpecies": "magby"}, {"id": 62, "filename": "chain-62.json", "baseSpecies": "pinsir"}, {"id": 63, "filename": "chain-63.json", "baseSpecies": "tauros"}, {"id": 64, "filename": "chain-64.json", "baseSpecies": "magi<PERSON><PERSON>"}, {"id": 65, "filename": "chain-65.json", "baseSpecies": "<PERSON>ras"}, {"id": 66, "filename": "chain-66.json", "baseSpecies": "ditto"}, {"id": 67, "filename": "chain-67.json", "baseSpecies": "eevee"}, {"id": 68, "filename": "chain-68.json", "baseSpecies": "porygon"}, {"id": 69, "filename": "chain-69.json", "baseSpecies": "omanyte"}, {"id": 70, "filename": "chain-70.json", "baseSpecies": "kabuto"}, {"id": 71, "filename": "chain-71.json", "baseSpecies": "aerodactyl"}, {"id": 72, "filename": "chain-72.json", "baseSpecies": "munchlax"}, {"id": 73, "filename": "chain-73.json", "baseSpecies": "articuno"}, {"id": 74, "filename": "chain-74.json", "baseSpecies": "zapdos"}, {"id": 75, "filename": "chain-75.json", "baseSpecies": "moltres"}, {"id": 76, "filename": "chain-76.json", "baseSpecies": "dratini"}, {"id": 77, "filename": "chain-77.json", "baseSpecies": "mewtwo"}, {"id": 78, "filename": "chain-78.json", "baseSpecies": "mew"}]