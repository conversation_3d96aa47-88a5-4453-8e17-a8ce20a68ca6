{"baby_trigger_item": null, "chain": {"evolution_details": [], "evolves_to": [{"evolution_details": [{"gender": null, "held_item": null, "item": null, "known_move": null, "known_move_type": null, "location": null, "min_affection": null, "min_beauty": null, "min_happiness": null, "min_level": 32, "needs_overworld_rain": false, "party_species": null, "party_type": null, "relative_physical_stats": null, "time_of_day": "", "trade_species": null, "trigger": {"name": "level-up", "url": "https://pokeapi.co/api/v2/evolution-trigger/1/"}, "turn_upside_down": false}], "evolves_to": [{"evolution_details": [{"gender": null, "held_item": {"name": "dragon-scale", "url": "https://pokeapi.co/api/v2/item/212/"}, "item": null, "known_move": null, "known_move_type": null, "location": null, "min_affection": null, "min_beauty": null, "min_happiness": null, "min_level": null, "needs_overworld_rain": false, "party_species": null, "party_type": null, "relative_physical_stats": null, "time_of_day": "", "trade_species": null, "trigger": {"name": "trade", "url": "https://pokeapi.co/api/v2/evolution-trigger/2/"}, "turn_upside_down": false}], "evolves_to": [], "is_baby": false, "species": {"name": "king<PERSON>", "url": "https://pokeapi.co/api/v2/pokemon-species/230/"}}], "is_baby": false, "species": {"name": "seadra", "url": "https://pokeapi.co/api/v2/pokemon-species/117/"}}], "is_baby": false, "species": {"name": "<PERSON>a", "url": "https://pokeapi.co/api/v2/pokemon-species/116/"}}, "id": 54}