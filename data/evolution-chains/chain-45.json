{"baby_trigger_item": null, "chain": {"evolution_details": [], "evolves_to": [{"evolution_details": [{"gender": null, "held_item": null, "item": {"name": "leaf-stone", "url": "https://pokeapi.co/api/v2/item/85/"}, "known_move": null, "known_move_type": null, "location": null, "min_affection": null, "min_beauty": null, "min_happiness": null, "min_level": null, "needs_overworld_rain": false, "party_species": null, "party_type": null, "relative_physical_stats": null, "time_of_day": "", "trade_species": null, "trigger": {"name": "use-item", "url": "https://pokeapi.co/api/v2/evolution-trigger/3/"}, "turn_upside_down": false}], "evolves_to": [], "is_baby": false, "species": {"name": "exeggutor", "url": "https://pokeapi.co/api/v2/pokemon-species/103/"}}], "is_baby": false, "species": {"name": "exeggcute", "url": "https://pokeapi.co/api/v2/pokemon-species/102/"}}, "id": 45}