{"baby_trigger_item": null, "chain": {"evolution_details": [], "evolves_to": [{"evolution_details": [{"gender": null, "held_item": {"name": "metal-coat", "url": "https://pokeapi.co/api/v2/item/210/"}, "item": null, "known_move": null, "known_move_type": null, "location": null, "min_affection": null, "min_beauty": null, "min_happiness": null, "min_level": null, "needs_overworld_rain": false, "party_species": null, "party_type": null, "relative_physical_stats": null, "time_of_day": "", "trade_species": null, "trigger": {"name": "trade", "url": "https://pokeapi.co/api/v2/evolution-trigger/2/"}, "turn_upside_down": false}], "evolves_to": [], "is_baby": false, "species": {"name": "s<PERSON><PERSON>", "url": "https://pokeapi.co/api/v2/pokemon-species/212/"}}, {"evolution_details": [{"gender": null, "held_item": null, "item": {"name": "black-augurite", "url": "https://pokeapi.co/api/v2/item/10001/"}, "known_move": null, "known_move_type": null, "location": null, "min_affection": null, "min_beauty": null, "min_happiness": null, "min_level": null, "needs_overworld_rain": false, "party_species": null, "party_type": null, "relative_physical_stats": null, "time_of_day": "", "trade_species": null, "trigger": {"name": "use-item", "url": "https://pokeapi.co/api/v2/evolution-trigger/3/"}, "turn_upside_down": false}], "evolves_to": [], "is_baby": false, "species": {"name": "kleavor", "url": "https://pokeapi.co/api/v2/pokemon-species/900/"}}], "is_baby": false, "species": {"name": "scyther", "url": "https://pokeapi.co/api/v2/pokemon-species/123/"}}, "id": 58}