{"name": "solar-beam", "power": 120, "accuracy": 100, "pp": 10, "type": "grass", "damage_class": "special", "effect_entries": [{"effect": "Inflicts regular damage.  User charges for one turn before attacking.\n\nDuring sunny day, the charge turn is skipped.\n\nDuring hail, rain dance, or sandstorm, power is halved.\n\nThis move cannot be selected by sleep talk.", "language": {"name": "en", "url": "https://pokeapi.co/api/v2/language/9/"}, "short_effect": "Requires a turn to charge before attacking."}]}