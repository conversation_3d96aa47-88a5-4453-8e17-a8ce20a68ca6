{"name": "metronome", "power": null, "accuracy": null, "pp": 10, "type": "normal", "damage_class": "status", "effect_entries": [{"effect": "Selects any move at random and uses it.  Moves the user already knows are not eligible.  Assist, meta, protection, and reflection moves are also not eligible; specifically, assist, chatter, copycat, counter, covet, destiny bond, detect, endure, feint, focus punch, follow me, helping hand, me first, metronome, mimic, mirror coat, mirror move, protect, quick guard, sketch, sleep talk, snatch, struggle, switcheroo, thief, trick, and wide guard will not be selected by this move.\n\nThis move cannot be copied by mimic or mirror move, nor selected by assist, metronome, or sleep talk.", "language": {"name": "en", "url": "https://pokeapi.co/api/v2/language/9/"}, "short_effect": "Randomly selects and uses any move in the game."}]}