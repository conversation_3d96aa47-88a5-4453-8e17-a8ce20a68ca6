{"name": "counter", "power": null, "accuracy": 100, "pp": 20, "type": "fighting", "damage_class": "physical", "effect_entries": [{"effect": "Targets the last opposing Pokémon to hit the user with a physical move this turn.  Inflicts twice the damage that move did to the user.  If there is no eligible target, this move will fail.  Type immunity applies, but other type effects are ignored.\n\nThis move cannot be copied by mirror move, nor selected by assist or metronome.", "language": {"name": "en", "url": "https://pokeapi.co/api/v2/language/9/"}, "short_effect": "Inflicts twice the damage the user received from the last physical hit it took."}]}