<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pokemon Battle Game</title>
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="screen active">
      <div class="loading-content">
        <h1>Pokemon Battle</h1>
        <div class="loading-spinner"></div>
        <p id="loading-text">Loading Pokemon data...</p>
      </div>
    </div>

    <!-- Main Menu Screen -->
    <div id="main-menu" class="screen">
      <div class="menu-content">
        <h1>Pokemon Battle</h1>
        <div class="menu-buttons">
          <button id="start-battle-btn" class="menu-button">
            Start Battle
          </button>
          <button id="select-trainer-btn" class="menu-button">
            Select Trainer
          </button>
          <button id="settings-btn" class="menu-button">Settings</button>
        </div>
      </div>
    </div>

    <!-- Trainer Selection Screen -->
    <div id="trainer-selection" class="screen">
      <div class="trainer-content">
        <h2>Select Opponent</h2>
        <div class="trainer-grid">
          <div class="trainer-card" data-difficulty="basic">
            <img
              src="assets/trainers/youngster.png"
              alt="Youngster"
              class="trainer-sprite"
            />
            <h3>Youngster Joey</h3>
            <p>Difficulty: Basic</p>
          </div>
          <div class="trainer-card" data-difficulty="standard">
            <img
              src="assets/trainers/ace-trainer.png"
              alt="Ace Trainer"
              class="trainer-sprite"
            />
            <h3>Ace Trainer</h3>
            <p>Difficulty: Standard</p>
          </div>
          <div class="trainer-card" data-difficulty="expert">
            <img
              src="assets/trainers/gym-leader.png"
              alt="Gym Leader"
              class="trainer-sprite"
            />
            <h3>Gym Leader</h3>
            <p>Difficulty: Expert</p>
          </div>
        </div>
        <button id="back-to-menu-btn" class="back-button">Back</button>
      </div>
    </div>

    <!-- Battle Screen -->
    <div id="battle-screen" class="screen">
      <!-- Battle Field -->
      <div class="battle-field">
        <!-- Enemy Info Area (Top Left) -->
        <div class="enemy-info-area">
          <div class="pokemon-info enemy-info">
            <div class="pokemon-name-level">
              <span id="enemy-name">Pikachu</span>
              <span id="enemy-level">Lv.50</span>
            </div>
            <div class="hp-bar-container">
              <div class="hp-bar">
                <div id="enemy-hp-fill" class="hp-fill"></div>
              </div>
              <div class="hp-text">
                <span id="enemy-hp-current">100</span>/<span id="enemy-hp-max"
                  >100</span
                >
              </div>
            </div>
            <div id="enemy-status" class="status-indicators"></div>
          </div>
        </div>

        <!-- Enemy Pokemon Area (Top Right) -->
        <div class="pokemon-area enemy-area">
          <div class="pokemon-sprite-container enemy-sprite-container">
            <img
              id="enemy-sprite"
              src=""
              alt="Enemy Pokemon"
              class="pokemon-sprite"
            />
          </div>
        </div>

        <!-- Player Pokemon Area (Bottom Left) -->
        <div class="pokemon-area player-area">
          <div class="pokemon-sprite-container player-sprite-container">
            <img
              id="player-sprite"
              src=""
              alt="Player Pokemon"
              class="pokemon-sprite"
            />
          </div>
        </div>

        <!-- Player Info Area (Bottom Right) -->
        <div class="player-info-area">
          <div class="pokemon-info player-info">
            <div class="pokemon-name-level">
              <span id="player-name">Charizard</span>
              <span id="player-level">Lv.50</span>
            </div>
            <div class="hp-bar-container">
              <div class="hp-bar">
                <div id="player-hp-fill" class="hp-fill"></div>
              </div>
              <div class="hp-text">
                <span id="player-hp-current">100</span>/<span id="player-hp-max"
                  >100</span
                >
              </div>
            </div>
            <div id="player-status" class="status-indicators"></div>
          </div>
        </div>
      </div>

      <!-- Battle Text Box -->
      <div id="battle-text-box" class="text-box">
        <p id="battle-text">What will Charizard do?</p>
      </div>

      <!-- Action Menu -->
      <div id="action-menu" class="menu-container active">
        <div class="action-grid">
          <button id="fight-btn" class="action-button">Fight</button>
          <button id="pokemon-btn" class="action-button">Pokemon</button>
          <button id="bag-btn" class="action-button">Bag</button>
          <button id="run-btn" class="action-button">Run</button>
        </div>
      </div>

      <!-- Move Selection Menu -->
      <div id="move-menu" class="menu-container">
        <div class="move-grid">
          <button class="move-button" data-move-index="0">
            <div class="move-name">Flamethrower</div>
            <div class="move-info">
              <span class="move-type fire">Fire</span>
              <span class="move-pp">PP: 15/15</span>
            </div>
          </button>
          <button class="move-button" data-move-index="1">
            <div class="move-name">Dragon Claw</div>
            <div class="move-info">
              <span class="move-type dragon">Dragon</span>
              <span class="move-pp">PP: 15/15</span>
            </div>
          </button>
          <button class="move-button" data-move-index="2">
            <div class="move-name">Fly</div>
            <div class="move-info">
              <span class="move-type flying">Flying</span>
              <span class="move-pp">PP: 15/15</span>
            </div>
          </button>
          <button class="move-button" data-move-index="3">
            <div class="move-name">Earthquake</div>
            <div class="move-info">
              <span class="move-type ground">Ground</span>
              <span class="move-pp">PP: 10/10</span>
            </div>
          </button>
        </div>
        <button id="back-to-actions-btn" class="back-button">Back</button>
      </div>

      <!-- Pokemon Team Menu -->
      <div id="pokemon-menu" class="menu-container">
        <div class="pokemon-team-grid">
          <!-- Pokemon team slots will be populated by JavaScript -->
        </div>
        <button id="back-to-actions-from-pokemon-btn" class="back-button">
          Back
        </button>
      </div>

      <!-- Bag Menu -->
      <div id="bag-menu" class="menu-container">
        <div class="bag-grid">
          <button class="item-button" data-item="potion">
            <div class="item-name">Potion</div>
            <div class="item-count">x5</div>
          </button>
          <button class="item-button" data-item="super-potion">
            <div class="item-name">Super Potion</div>
            <div class="item-count">x3</div>
          </button>
          <button class="item-button" data-item="hyper-potion">
            <div class="item-name">Hyper Potion</div>
            <div class="item-count">x1</div>
          </button>
        </div>
        <button id="back-to-actions-from-bag-btn" class="back-button">
          Back
        </button>
      </div>
    </div>

    <!-- Battle Result Screen -->
    <div id="battle-result" class="screen">
      <div class="result-content">
        <h2 id="result-title">Victory!</h2>
        <p id="result-message">You defeated the opponent!</p>
        <div class="result-buttons">
          <button id="battle-again-btn" class="menu-button">
            Battle Again
          </button>
          <button id="return-menu-btn" class="menu-button">Main Menu</button>
        </div>
      </div>
    </div>

    <!-- JavaScript Files -->
    <script src="js/config.js"></script>
    <script src="js/offline-data-loader.js"></script>
    <script src="js/api.js"></script>
    <script src="js/pokemon.js"></script>
    <script src="js/battle-system.js"></script>
    <script src="js/ai.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
  </body>
</html>
