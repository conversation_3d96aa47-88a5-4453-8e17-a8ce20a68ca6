// Pokemon API Module
class PokemonAPI {
  constructor() {
    this.baseUrl = CONFIG.API.BASE_URL;
    this.cache = new Map();
    this.loadingPromises = new Map();
    this.requestQueue = [];
    this.isProcessingQueue = false;

    // Load cached data from localStorage
    this.loadCacheFromStorage();
  }

  // Load cached data from localStorage
  loadCacheFromStorage() {
    try {
      const cachedData = localStorage.getItem(CONFIG.STORAGE_KEYS.POKEMON_DATA);
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        const now = Date.now();

        // Check if cache is still valid
        if (
          parsed.timestamp &&
          now - parsed.timestamp < CONFIG.API.CACHE_DURATION
        ) {
          this.cache = new Map(parsed.data);
          console.log("Loaded Pokemon data from cache");
        } else {
          // Cache expired, clear it
          localStorage.removeItem(CONFIG.STORAGE_KEYS.POKEMON_DATA);
          console.log("Cache expired, will fetch fresh data");
        }
      }
    } catch (error) {
      console.error("Error loading cache from storage:", error);
      localStorage.removeItem(CONFIG.STORAGE_KEYS.POKEMON_DATA);
    }
  }

  // Save cache to localStorage
  saveCacheToStorage() {
    try {
      const cacheData = {
        timestamp: Date.now(),
        data: Array.from(this.cache.entries()),
      };
      localStorage.setItem(
        CONFIG.STORAGE_KEYS.POKEMON_DATA,
        JSON.stringify(cacheData)
      );
      console.log("Saved Pokemon data to cache");
    } catch (error) {
      console.error("Error saving cache to storage:", error);
    }
  }

  // Add request to queue to avoid rate limiting
  async queueRequest(url) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ url, resolve, reject });
      this.processQueue();
    });
  }

  // Process request queue with delays
  async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const { url, resolve, reject } = this.requestQueue.shift();

      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        resolve(data);
      } catch (error) {
        reject(error);
      }

      // Add delay between requests to avoid rate limiting
      if (this.requestQueue.length > 0) {
        await new Promise((resolve) =>
          setTimeout(resolve, CONFIG.API.REQUEST_DELAY)
        );
      }
    }

    this.isProcessingQueue = false;
  }

  // Fetch Pokemon data by ID
  async fetchPokemon(id) {
    const cacheKey = `pokemon_${id}`;

    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Check if already loading
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    // Create loading promise
    const loadingPromise = this.loadPokemonData(id);
    this.loadingPromises.set(cacheKey, loadingPromise);

    try {
      const pokemon = await loadingPromise;
      this.cache.set(cacheKey, pokemon);
      this.loadingPromises.delete(cacheKey);
      return pokemon;
    } catch (error) {
      this.loadingPromises.delete(cacheKey);
      throw error;
    }
  }

  // Load complete Pokemon data
  async loadPokemonData(id) {
    try {
      // Fetch basic Pokemon data
      const pokemonData = await this.queueRequest(
        `${this.baseUrl}/pokemon/${id}`
      );

      // Fetch species data for additional info
      const speciesData = await this.queueRequest(pokemonData.species.url);

      // Process and structure the data
      const pokemon = {
        id: pokemonData.id,
        name: pokemonData.name,
        types: pokemonData.types.map((type) => type.type.name),
        baseStats: {
          hp: pokemonData.stats.find((stat) => stat.stat.name === "hp")
            .base_stat,
          attack: pokemonData.stats.find((stat) => stat.stat.name === "attack")
            .base_stat,
          defense: pokemonData.stats.find(
            (stat) => stat.stat.name === "defense"
          ).base_stat,
          specialAttack: pokemonData.stats.find(
            (stat) => stat.stat.name === "special-attack"
          ).base_stat,
          specialDefense: pokemonData.stats.find(
            (stat) => stat.stat.name === "special-defense"
          ).base_stat,
          speed: pokemonData.stats.find((stat) => stat.stat.name === "speed")
            .base_stat,
        },
        sprites: {
          front: pokemonData.sprites.front_default,
          back: pokemonData.sprites.back_default,
          frontShiny: pokemonData.sprites.front_shiny,
          backShiny: pokemonData.sprites.back_shiny,
        },
        height: pokemonData.height,
        weight: pokemonData.weight,
        moves: await this.processPokemonMoves(pokemonData.moves.slice(0, 20)), // Limit moves for performance
      };

      return pokemon;
    } catch (error) {
      console.error(`Error loading Pokemon ${id}:`, error);
      throw error;
    }
  }

  // Process Pokemon moves
  async processPokemonMoves(moveList) {
    const moves = [];
    const movePromises = moveList.slice(0, 8).map(async (moveEntry) => {
      try {
        const moveData = await this.queueRequest(moveEntry.move.url);
        return {
          name: moveData.name,
          power: moveData.power,
          accuracy: moveData.accuracy,
          pp: moveData.pp,
          type: moveData.type.name,
          damageClass: moveData.damage_class.name,
          description:
            moveData.effect_entries.find(
              (entry) => entry.language.name === "en"
            )?.short_effect || "No description available",
        };
      } catch (error) {
        console.error(`Error loading move ${moveEntry.move.name}:`, error);
        return null;
      }
    });

    const resolvedMoves = await Promise.all(movePromises);
    return resolvedMoves.filter((move) => move !== null);
  }

  // Fetch multiple Pokemon (for initial loading)
  async fetchPokemonBatch(startId, endId) {
    const promises = [];
    for (let id = startId; id <= endId; id++) {
      promises.push(this.fetchPokemon(id));
    }

    try {
      const results = await Promise.all(promises);
      this.saveCacheToStorage(); // Save after batch loading
      return results;
    } catch (error) {
      console.error("Error fetching Pokemon batch:", error);
      throw error;
    }
  }

  // Get Pokemon by name (case insensitive)
  async getPokemonByName(name) {
    const normalizedName = name.toLowerCase().trim();

    // Search in cache first
    for (const [key, pokemon] of this.cache.entries()) {
      if (pokemon.name.toLowerCase() === normalizedName) {
        return pokemon;
      }
    }

    // If not in cache, try to fetch by name
    try {
      const pokemonData = await this.queueRequest(
        `${this.baseUrl}/pokemon/${normalizedName}`
      );
      return await this.loadPokemonData(pokemonData.id);
    } catch (error) {
      console.error(`Pokemon "${name}" not found:`, error);
      return null;
    }
  }

  // Fetch Pokemon species data (includes evolution chain)
  async fetchPokemonSpecies(id) {
    const cacheKey = `species_${id}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const speciesData = await this.queueRequest(
        `${this.baseUrl}/pokemon-species/${id}`
      );
      this.cache.set(cacheKey, speciesData);
      return speciesData;
    } catch (error) {
      console.error(`Error fetching species data for ${id}:`, error);
      throw error;
    }
  }

  // Fetch evolution chain data
  async fetchEvolutionChain(chainUrl) {
    const cacheKey = `evolution_${chainUrl}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const evolutionData = await this.queueRequest(chainUrl);
      this.cache.set(cacheKey, evolutionData);
      return evolutionData;
    } catch (error) {
      console.error(`Error fetching evolution chain:`, error);
      throw error;
    }
  }

  // Get level-up moves for a Pokemon
  async fetchLevelUpMoves(pokemonId) {
    const cacheKey = `moves_${pokemonId}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const pokemonData = await this.queueRequest(
        `${this.baseUrl}/pokemon/${pokemonId}`
      );

      // Extract level-up moves
      const allLevelUpMoves = pokemonData.moves
        .flatMap((move) =>
          move.version_group_details
            .filter((vgd) => vgd.move_learn_method.name === "level-up")
            .map((vgd) => ({
              move: move.move.name,
              moveUrl: move.move.url,
              level: vgd.level_learned_at,
              version: vgd.version_group.name,
            }))
        )
        .filter((m) => m.level > 0);

      // Remove duplicates - keep the earliest level for each move
      const moveMap = new Map();
      allLevelUpMoves.forEach((moveEntry) => {
        const moveName = moveEntry.move;
        if (
          !moveMap.has(moveName) ||
          moveMap.get(moveName).level > moveEntry.level
        ) {
          moveMap.set(moveName, moveEntry);
        }
      });

      // Convert back to array and sort by level
      const levelUpMoves = Array.from(moveMap.values()).sort(
        (a, b) => a.level - b.level
      );

      this.cache.set(cacheKey, levelUpMoves);
      return levelUpMoves;
    } catch (error) {
      console.error(`Error fetching level-up moves for ${pokemonId}:`, error);
      throw error;
    }
  }

  // Get random Pokemon from cache
  getRandomPokemon() {
    const cachedPokemon = Array.from(this.cache.values()).filter(
      (pokemon) => pokemon.id <= CONFIG.API.POKEMON_LIMIT
    );
    if (cachedPokemon.length === 0) {
      return null;
    }
    return cachedPokemon[Math.floor(Math.random() * cachedPokemon.length)];
  }

  // Check if Pokemon data is loaded
  isPokemonLoaded(id) {
    return this.cache.has(`pokemon_${id}`);
  }

  // Get loading progress
  getLoadingProgress() {
    const totalPokemon = CONFIG.API.POKEMON_LIMIT;
    const loadedPokemon = Array.from(this.cache.keys()).filter(
      (key) =>
        key.startsWith("pokemon_") &&
        parseInt(key.split("_")[1]) <= totalPokemon
    ).length;

    return {
      loaded: loadedPokemon,
      total: totalPokemon,
      percentage: Math.round((loadedPokemon / totalPokemon) * 100),
    };
  }

  // Preload essential Pokemon for the game
  async preloadEssentialPokemon() {
    const essentialIds = [
      ...CONFIG.PLAYER_TEAM.map((p) => p.id),
      ...Object.values(CONFIG.TRAINERS).flatMap((trainer) =>
        trainer.team.map((p) => p.id)
      ),
    ];

    const uniqueIds = [...new Set(essentialIds)];
    console.log("Preloading essential Pokemon:", uniqueIds);

    try {
      await Promise.all(uniqueIds.map((id) => this.fetchPokemon(id)));
      console.log("Essential Pokemon loaded successfully");
    } catch (error) {
      console.error("Error preloading essential Pokemon:", error);
      throw error;
    }
  }

  // Load all first generation Pokemon (background loading)
  async loadAllFirstGenPokemon(progressCallback) {
    console.log("Starting to load all first generation Pokemon...");

    const batchSize = 10;
    const totalPokemon = CONFIG.API.POKEMON_LIMIT;

    for (let i = 1; i <= totalPokemon; i += batchSize) {
      const endId = Math.min(i + batchSize - 1, totalPokemon);

      try {
        await this.fetchPokemonBatch(i, endId);

        if (progressCallback) {
          const progress = this.getLoadingProgress();
          progressCallback(progress);
        }

        console.log(`Loaded Pokemon ${i}-${endId}`);
      } catch (error) {
        console.error(`Error loading Pokemon batch ${i}-${endId}:`, error);
        // Continue with next batch even if one fails
      }
    }

    console.log("Finished loading all first generation Pokemon");
    this.saveCacheToStorage();
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    localStorage.removeItem(CONFIG.STORAGE_KEYS.POKEMON_DATA);
    console.log("Pokemon cache cleared");
  }

  // Clear move cache specifically (for testing move fixes)
  clearMoveCache() {
    const moveCacheKeys = Array.from(this.cache.keys()).filter((key) =>
      key.startsWith("moves_")
    );
    moveCacheKeys.forEach((key) => this.cache.delete(key));
    console.log(`Cleared ${moveCacheKeys.length} move cache entries`);
  }
}

// Create global API instance
const pokemonAPI = new PokemonAPI();
