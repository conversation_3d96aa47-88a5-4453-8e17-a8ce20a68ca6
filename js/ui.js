// UI System for Pokemon Battle Game

class UIManager {
  constructor() {
    this.animationQueue = [];
    this.isAnimating = false;
    this.textSpeed = CONFIG.UI.TEXT_SPEED;
    this.setupEventListeners();
  }

  // Setup global UI event listeners
  setupEventListeners() {
    // Handle window resize for responsive design
    window.addEventListener("resize", () => {
      this.handleResize();
    });

    // Handle touch events for mobile
    document.addEventListener("touchstart", (e) => {
      this.handleTouchStart(e);
    });

    // Handle keyboard shortcuts
    document.addEventListener("keydown", (e) => {
      this.handleGlobalKeyPress(e);
    });
  }

  // Handle window resize
  handleResize() {
    // Adjust UI elements for different screen sizes
    const battleScreen = document.getElementById("battle-screen");
    if (battleScreen && battleScreen.classList.contains("active")) {
      this.adjustBattleScreenLayout();
    }
  }

  // Adjust battle screen layout for current screen size
  adjustBattleScreenLayout() {
    const battleField = document.querySelector(".battle-field");
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    // Adjust Pokemon sprite sizes based on screen size
    const sprites = document.querySelectorAll(".pokemon-sprite");
    sprites.forEach((sprite) => {
      if (screenWidth < 480) {
        sprite.style.width = "80px";
        sprite.style.height = "80px";
      } else if (screenWidth < 768) {
        sprite.style.width = "100px";
        sprite.style.height = "100px";
      } else {
        sprite.style.width = "120px";
        sprite.style.height = "120px";
      }
    });
  }

  // Handle touch start for mobile interactions
  handleTouchStart(e) {
    // Add touch feedback
    const target = e.target;
    if (
      target.classList.contains("action-button") ||
      target.classList.contains("move-button") ||
      target.classList.contains("menu-button")
    ) {
      target.style.transform = "scale(0.95)";
      setTimeout(() => {
        target.style.transform = "";
      }, 150);
    }
  }

  // Handle global keyboard shortcuts
  handleGlobalKeyPress(e) {
    // Global shortcuts that work across all screens
    switch (e.key) {
      case "F11":
        e.preventDefault();
        this.toggleFullscreen();
        break;
      case "Escape":
        if (document.fullscreenElement) {
          document.exitFullscreen();
        }
        break;
    }
  }

  // Toggle fullscreen mode
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.log("Error attempting to enable fullscreen:", err);
      });
    } else {
      document.exitFullscreen();
    }
  }

  // Animate HP bar changes
  animateHPBar(side, pokemon, duration = CONFIG.UI.HP_BAR_ANIMATION_SPEED) {
    return new Promise((resolve) => {
      const hpFill = document.getElementById(`${side}-hp-fill`);
      const hpCurrent = document.getElementById(`${side}-hp-current`);
      const targetPercentage = pokemon.getHPPercentage();

      // Get current percentage
      const currentWidth = parseFloat(hpFill.style.width) || 100;

      // Animate the change
      const startTime = Date.now();
      const startWidth = currentWidth;
      const widthDiff = targetPercentage - startWidth;

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeProgress = 1 - Math.pow(1 - progress, 3);

        const currentPercentage = startWidth + widthDiff * easeProgress;
        hpFill.style.width = `${currentPercentage}%`;

        // Update HP text
        const displayHP = Math.round((currentPercentage / 100) * pokemon.maxHP);
        hpCurrent.textContent = Math.max(0, displayHP);

        // Update HP bar color
        this.updateHPBarColor(hpFill, currentPercentage);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // Ensure final values are correct
          hpFill.style.width = `${targetPercentage}%`;
          hpCurrent.textContent = pokemon.currentHP;
          this.updateHPBarColor(hpFill, targetPercentage);
          resolve();
        }
      };

      requestAnimationFrame(animate);
    });
  }

  // Update HP bar color based on percentage
  updateHPBarColor(hpFill, percentage) {
    hpFill.className = "hp-fill";
    if (percentage < 10) {
      hpFill.classList.add("critical");
    } else if (percentage < 25) {
      hpFill.classList.add("low");
    }
  }

  // Animate damage taken
  animateDamage(side, damage) {
    return new Promise((resolve) => {
      const sprite = document.getElementById(`${side}-sprite`);
      const damageText = document.createElement("div");

      // Create damage number
      damageText.className = "damage-number";
      damageText.textContent = `-${damage}`;
      damageText.style.position = "absolute";
      damageText.style.color = "#ff4444";
      damageText.style.fontFamily = "Press Start 2P";
      damageText.style.fontSize = "14px";
      damageText.style.fontWeight = "bold";
      damageText.style.zIndex = "1000";
      damageText.style.pointerEvents = "none";

      // Position damage text
      const spriteRect = sprite.getBoundingClientRect();
      damageText.style.left = `${spriteRect.left + spriteRect.width / 2}px`;
      damageText.style.top = `${spriteRect.top}px`;

      document.body.appendChild(damageText);

      // Animate sprite shake
      sprite.classList.add("damaged");

      // Animate damage number
      let startTime = Date.now();
      const duration = 1000;

      const animateDamageNumber = () => {
        const elapsed = Date.now() - startTime;
        const progress = elapsed / duration;

        if (progress < 1) {
          const yOffset = -50 * progress;
          const opacity = 1 - progress;

          damageText.style.transform = `translateY(${yOffset}px) translateX(-50%)`;
          damageText.style.opacity = opacity;

          requestAnimationFrame(animateDamageNumber);
        } else {
          document.body.removeChild(damageText);
          sprite.classList.remove("damaged");
          resolve();
        }
      };

      requestAnimationFrame(animateDamageNumber);
    });
  }

  // Animate missed attack
  animateMiss(side) {
    return new Promise((resolve) => {
      const sprite = document.getElementById(`${side}-sprite`);
      const missText = document.createElement("div");

      // Create miss text
      missText.className = "miss-text";
      missText.textContent = "Missed";
      missText.style.position = "absolute";
      missText.style.color = "#888888";
      missText.style.fontFamily = "Press Start 2P";
      missText.style.fontSize = "14px";
      missText.style.fontWeight = "bold";
      missText.style.zIndex = "1000";
      missText.style.pointerEvents = "none";

      // Position miss text
      const spriteRect = sprite.getBoundingClientRect();
      missText.style.left = `${spriteRect.left + spriteRect.width / 2}px`;
      missText.style.top = `${spriteRect.top}px`;

      document.body.appendChild(missText);

      // Animate miss text (no sprite shake for misses)
      let startTime = Date.now();
      const duration = 1000;

      const animateMissText = () => {
        const elapsed = Date.now() - startTime;
        const progress = elapsed / duration;

        if (progress < 1) {
          const yOffset = -30 * progress;
          const opacity = 1 - progress;

          missText.style.transform = `translateY(${yOffset}px) translateX(-50%)`;
          missText.style.opacity = opacity;

          requestAnimationFrame(animateMissText);
        } else {
          document.body.removeChild(missText);
          resolve();
        }
      };

      requestAnimationFrame(animateMissText);
    });
  }

  // Type text with typewriter effect
  typeText(element, text, speed = this.textSpeed) {
    return new Promise((resolve) => {
      element.textContent = "";
      let index = 0;

      const typeChar = () => {
        if (index < text.length) {
          element.textContent += text[index];
          index++;
          setTimeout(typeChar, 1000 / speed);
        } else {
          resolve();
        }
      };

      typeChar();
    });
  }

  // Show notification message
  showNotification(message, type = "info", duration = 3000) {
    const notification = document.createElement("div");
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    Object.assign(notification.style, {
      position: "fixed",
      top: "20px",
      right: "20px",
      padding: "1rem",
      borderRadius: "8px",
      color: "white",
      fontFamily: "Press Start 2P",
      fontSize: "10px",
      zIndex: "10000",
      maxWidth: "300px",
      wordWrap: "break-word",
    });

    // Set background color based on type
    switch (type) {
      case "success":
        notification.style.background = "#4CAF50";
        break;
      case "error":
        notification.style.background = "#F44336";
        break;
      case "warning":
        notification.style.background = "#FF9800";
        break;
      default:
        notification.style.background = "#2196F3";
    }

    document.body.appendChild(notification);

    // Animate in
    notification.style.transform = "translateX(100%)";
    notification.style.transition = "transform 0.3s ease";

    setTimeout(() => {
      notification.style.transform = "translateX(0)";
    }, 10);

    // Remove after duration
    setTimeout(() => {
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (notification.parentNode) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, duration);
  }

  // Add loading spinner to element
  addLoadingSpinner(element, text = "Loading...") {
    const spinner = document.createElement("div");
    spinner.className = "ui-loading-spinner";
    spinner.innerHTML = `
            <div class="spinner"></div>
            <div class="loading-text">${text}</div>
        `;

    // Add spinner styles
    const style = document.createElement("style");
    style.textContent = `
            .ui-loading-spinner {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1rem;
                padding: 2rem;
            }
            .ui-loading-spinner .spinner {
                width: 30px;
                height: 30px;
                border: 3px solid rgba(255,255,255,0.3);
                border-top: 3px solid #ffd700;
                border-radius: 50%;
                animation: ui-spin 1s linear infinite;
            }
            .ui-loading-spinner .loading-text {
                color: white;
                font-size: 10px;
                text-align: center;
            }
            @keyframes ui-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;

    if (!document.querySelector("#ui-spinner-styles")) {
      style.id = "ui-spinner-styles";
      document.head.appendChild(style);
    }

    element.appendChild(spinner);
    return spinner;
  }

  // Remove loading spinner
  removeLoadingSpinner(element) {
    const spinner = element.querySelector(".ui-loading-spinner");
    if (spinner) {
      element.removeChild(spinner);
    }
  }

  // Smooth scroll to element
  scrollToElement(element, duration = 500) {
    const targetPosition = element.offsetTop;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    const animation = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const run = this.easeInOutQuad(
        timeElapsed,
        startPosition,
        distance,
        duration
      );
      window.scrollTo(0, run);
      if (timeElapsed < duration) requestAnimationFrame(animation);
    };

    requestAnimationFrame(animation);
  }

  // Easing function for smooth animations
  easeInOutQuad(t, b, c, d) {
    t /= d / 2;
    if (t < 1) return (c / 2) * t * t + b;
    t--;
    return (-c / 2) * (t * (t - 2) - 1) + b;
  }
}

// Create global UI manager instance
const uiManager = new UIManager();

console.log("UI System module loaded");
