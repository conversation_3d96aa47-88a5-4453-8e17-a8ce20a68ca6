// Battle System Implementation

class BattleSystem {
  constructor(playerTeam, enemyTeam, trainer) {
    this.playerTeam = playerTeam;
    this.enemyTeam = enemyTeam;
    this.trainer = trainer;

    // Current active Pokemon
    this.currentPlayerPokemon = playerTeam[0];
    this.currentEnemyPokemon = enemyTeam[0];

    // Battle state
    this.battleState = "start"; // start, selectAction, selectMove, selectTarget, executing, end
    this.turnQueue = [];
    this.battleLog = [];
    this.winner = null;

    // UI references
    this.battleText = document.getElementById("battle-text");
    this.actionMenu = document.getElementById("action-menu");
    this.moveMenu = document.getElementById("move-menu");
    this.pokemonMenu = document.getElementById("pokemon-menu");
    this.bagMenu = document.getElementById("bag-menu");

    // Event handlers
    this.setupEventHandlers();

    // AI system
    this.ai = new PokemonAI(trainer.difficulty);
  }

  // Setup event handlers for battle UI
  setupEventHandlers() {
    // Action menu buttons
    document
      .getElementById("fight-btn")
      .addEventListener("click", () => this.showMoveMenu());
    document
      .getElementById("pokemon-btn")
      .addEventListener("click", () => this.showPokemonMenu());
    document
      .getElementById("bag-btn")
      .addEventListener("click", () => this.showBagMenu());
    document
      .getElementById("run-btn")
      .addEventListener("click", () => this.attemptRun());

    // Move menu
    document.querySelectorAll(".move-button").forEach((button, index) => {
      button.addEventListener("click", () => this.selectMove(index));
    });
    document
      .getElementById("back-to-actions-btn")
      .addEventListener("click", () => this.showActionMenu());

    // Pokemon menu
    document
      .getElementById("back-to-actions-from-pokemon-btn")
      .addEventListener("click", () => this.showActionMenu());

    // Bag menu
    document.querySelectorAll(".item-button").forEach((button) => {
      button.addEventListener("click", () => this.useItem(button.dataset.item));
    });
    document
      .getElementById("back-to-actions-from-bag-btn")
      .addEventListener("click", () => this.showActionMenu());
  }

  // Start the battle
  async startBattle() {
    console.log("Battle started!");
    this.battleState = "start";

    // Update UI with initial Pokemon
    this.updateBattleUI();

    // Show battle start messages
    await this.showMessage(`${this.trainer.name} wants to battle!`);
    await this.showMessage(
      `${this.trainer.name} sent out ${this.currentEnemyPokemon.name}!`
    );
    await this.showMessage(`Go! ${this.currentPlayerPokemon.name}!`);

    // Start the battle loop
    this.battleState = "selectAction";
    this.showActionMenu();
    this.showMessage(`What will ${this.currentPlayerPokemon.name} do?`);
  }

  // Update battle UI with current Pokemon info
  updateBattleUI() {
    // Update player Pokemon
    this.updatePokemonDisplay("player", this.currentPlayerPokemon);

    // Update enemy Pokemon
    this.updatePokemonDisplay("enemy", this.currentEnemyPokemon);

    // Update move menu with current Pokemon's moves
    this.updateMoveMenu();
  }

  // Update Pokemon display (player or enemy)
  updatePokemonDisplay(side, pokemon) {
    const nameElement = document.getElementById(`${side}-name`);
    const levelElement = document.getElementById(`${side}-level`);
    const hpCurrentElement = document.getElementById(`${side}-hp-current`);
    const hpMaxElement = document.getElementById(`${side}-hp-max`);
    const spriteElement = document.getElementById(`${side}-sprite`);
    const statusElement = document.getElementById(`${side}-status`);

    nameElement.textContent =
      pokemon.name.charAt(0).toUpperCase() + pokemon.name.slice(1);
    levelElement.textContent = `Lv.${pokemon.level}`;
    hpCurrentElement.textContent = pokemon.currentHP;
    hpMaxElement.textContent = pokemon.maxHP;

    // Update sprite
    const spriteUrl =
      side === "player" ? pokemon.sprites.back : pokemon.sprites.front;
    if (spriteUrl) {
      spriteElement.src = spriteUrl;
      spriteElement.style.display = "block";
    } else {
      spriteElement.style.display = "none";
    }

    // Update HP bar
    this.updateHPBar(side, pokemon);

    // Update status indicators
    this.updateStatusDisplay(side, pokemon);
  }

  // Update HP bar (with animation if UI manager is available)
  async updateHPBar(side, pokemon, animate = true) {
    if (animate && typeof uiManager !== "undefined") {
      await uiManager.animateHPBar(side, pokemon);
    } else {
      // Fallback to instant update
      const hpFill = document.getElementById(`${side}-hp-fill`);
      const hpCurrent = document.getElementById(`${side}-hp-current`);
      const percentage = pokemon.getHPPercentage();

      hpFill.style.width = `${percentage}%`;
      hpCurrent.textContent = pokemon.currentHP;

      // Update HP bar color based on percentage
      hpFill.className = "hp-fill";
      if (percentage < 10) {
        hpFill.classList.add("critical");
      } else if (percentage < 25) {
        hpFill.classList.add("low");
      }
    }
  }

  // Update status display
  updateStatusDisplay(side, pokemon) {
    const statusElement = document.getElementById(`${side}-status`);
    statusElement.innerHTML = "";

    if (pokemon.status) {
      const statusSpan = document.createElement("span");
      statusSpan.className = `status-indicator status-${pokemon.status}`;
      statusSpan.textContent = pokemon.status.toUpperCase();
      statusElement.appendChild(statusSpan);
    }

    if (pokemon.confusion) {
      const confusionSpan = document.createElement("span");
      confusionSpan.className = "status-indicator status-confusion";
      confusionSpan.textContent = "CONFUSED";
      statusElement.appendChild(confusionSpan);
    }
  }

  // Update move menu with current Pokemon's moves
  updateMoveMenu() {
    const moveButtons = document.querySelectorAll(".move-button");
    const moves = this.currentPlayerPokemon.moves;

    moveButtons.forEach((button, index) => {
      if (index < moves.length) {
        const move = moves[index];
        const nameElement = button.querySelector(".move-name");
        const typeElement = button.querySelector(".move-type");
        const ppElement = button.querySelector(".move-pp");

        nameElement.textContent =
          move.name.charAt(0).toUpperCase() + move.name.slice(1);
        typeElement.textContent =
          move.type.charAt(0).toUpperCase() + move.type.slice(1);
        typeElement.className = `move-type ${move.type}`;
        ppElement.textContent = `PP: ${move.currentPP}/${move.pp}`;

        // Disable button if no PP left or Pokemon can't use move
        button.disabled = !this.currentPlayerPokemon.canUseMove(index);
        button.style.display = "block";
      } else {
        button.style.display = "none";
      }
    });
  }

  // Show battle message with delay
  async showMessage(text, delay = 2000) {
    this.battleText.textContent = text;
    this.addToBattleLog(text);
    return new Promise((resolve) => setTimeout(resolve, delay));
  }

  // Add message to battle log
  addToBattleLog(message) {
    this.battleLog.push({
      timestamp: Date.now(),
      message: message,
    });
  }

  // Show action menu
  showActionMenu() {
    this.hideAllMenus();
    this.actionMenu.classList.add("active");
    this.battleState = "selectAction";
  }

  // Show move menu
  showMoveMenu() {
    this.hideAllMenus();
    this.moveMenu.classList.add("active");
    this.updateMoveMenu();
    this.battleState = "selectMove";
  }

  // Show Pokemon menu
  showPokemonMenu() {
    this.hideAllMenus();
    this.pokemonMenu.classList.add("active");
    this.updatePokemonMenu();
    this.battleState = "selectPokemon";
  }

  // Show bag menu
  showBagMenu() {
    this.hideAllMenus();
    this.bagMenu.classList.add("active");
    this.battleState = "selectItem";
  }

  // Hide all menus
  hideAllMenus() {
    document.querySelectorAll(".menu-container").forEach((menu) => {
      menu.classList.remove("active");
    });
  }

  // Update Pokemon menu
  updatePokemonMenu() {
    const pokemonGrid = document.querySelector(".pokemon-team-grid");
    pokemonGrid.innerHTML = "";

    this.playerTeam.forEach((pokemon, index) => {
      const pokemonCard = document.createElement("div");
      pokemonCard.className = "pokemon-card";
      if (pokemon === this.currentPlayerPokemon) {
        pokemonCard.classList.add("active");
      }
      if (pokemon.fainted) {
        pokemonCard.classList.add("fainted");
      }

      pokemonCard.innerHTML = `
                <div class="pokemon-card-sprite">
                    <img src="${pokemon.sprites.front || ""}" alt="${
        pokemon.name
      }">
                </div>
                <div class="pokemon-card-info">
                    <div class="pokemon-card-name">${pokemon.name}</div>
                    <div class="pokemon-card-level">Lv.${pokemon.level}</div>
                    <div class="pokemon-card-hp">HP: ${pokemon.currentHP}/${
        pokemon.maxHP
      }</div>
                    <div class="pokemon-card-status">${pokemon.getStatusText()}</div>
                </div>
            `;

      if (!pokemon.fainted && pokemon !== this.currentPlayerPokemon) {
        pokemonCard.addEventListener("click", () => this.switchPokemon(index));
      }

      pokemonGrid.appendChild(pokemonCard);
    });
  }

  // Select move
  async selectMove(moveIndex) {
    if (!this.currentPlayerPokemon.canUseMove(moveIndex)) {
      await this.showMessage("That move can't be used right now!", 1000);
      return;
    }

    const move = this.currentPlayerPokemon.moves[moveIndex];
    console.log(`Player selected move: ${move.name}`);

    // Add player action to turn queue
    this.turnQueue.push({
      type: "move",
      pokemon: this.currentPlayerPokemon,
      move: move,
      moveIndex: moveIndex,
      target: this.currentEnemyPokemon,
      priority: move.priority || 0,
      speed: this.currentPlayerPokemon.stats.speed,
    });

    // Get AI action
    const aiAction = this.ai.selectAction(
      this.currentEnemyPokemon,
      this.currentPlayerPokemon,
      this.enemyTeam
    );
    this.turnQueue.push(aiAction);

    // Execute turn
    await this.executeTurn();
  }

  // Switch Pokemon
  async switchPokemon(pokemonIndex) {
    const newPokemon = this.playerTeam[pokemonIndex];

    if (newPokemon.fainted) {
      await this.showMessage(`${newPokemon.name} is unable to battle!`, 1000);
      return;
    }

    if (newPokemon === this.currentPlayerPokemon) {
      await this.showMessage(`${newPokemon.name} is already in battle!`, 1000);
      return;
    }

    // Add switch action to turn queue
    this.turnQueue.push({
      type: "switch",
      pokemon: this.currentPlayerPokemon,
      newPokemon: newPokemon,
      priority: 6, // Switching has high priority
      speed: 999,
    });

    // Get AI action
    const aiAction = this.ai.selectAction(
      this.currentEnemyPokemon,
      newPokemon,
      this.enemyTeam
    );
    this.turnQueue.push(aiAction);

    // Execute turn
    await this.executeTurn();
  }

  // Use item
  async useItem(itemType) {
    const item = CONFIG.ITEMS[itemType.toUpperCase().replace("-", "_")];
    if (!item) {
      await this.showMessage("That item doesn't exist!", 1000);
      return;
    }

    if (item.type === "heal") {
      if (this.currentPlayerPokemon.fainted) {
        await this.showMessage(
          "You can't use that on a fainted Pokemon!",
          1000
        );
        this.showActionMenu();
        return;
      }

      const healAmount = this.currentPlayerPokemon.heal(item.value);
      if (healAmount > 0) {
        await this.showMessage(`You used ${item.name}!`);
        await this.showMessage(
          `${this.currentPlayerPokemon.name} recovered ${healAmount} HP!`
        );

        // Animate HP recovery
        await this.updateHPBar("player", this.currentPlayerPokemon, true);

        // Get AI action and continue battle
        const aiAction = this.ai.selectAction(
          this.currentEnemyPokemon,
          this.currentPlayerPokemon,
          this.enemyTeam
        );
        this.turnQueue.push(aiAction);

        // Execute AI turn
        await this.executeAction(aiAction);
        await this.endTurn();
      } else {
        await this.showMessage(
          `${this.currentPlayerPokemon.name} is already at full health!`,
          1000
        );
        this.showActionMenu();
      }
    }
  }

  // Attempt to run from battle
  async attemptRun() {
    // For trainer battles, running is not allowed
    await this.showMessage("You can't run from a trainer battle!", 1500);
    this.showActionMenu();
  }

  // Execute turn with both actions
  async executeTurn() {
    this.battleState = "executing";
    this.hideAllMenus();

    // Sort actions by priority and speed
    this.turnQueue.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // Higher priority goes first
      }
      return b.speed - a.speed; // Higher speed goes first
    });

    // Execute each action
    for (const action of this.turnQueue) {
      await this.executeAction(action);

      // Check if battle should end
      if (this.checkBattleEnd()) {
        return;
      }
    }

    // End turn processing
    await this.endTurn();
  }

  // Execute a single action
  async executeAction(action) {
    switch (action.type) {
      case "move":
        await this.executeMove(action);
        break;
      case "switch":
        await this.executeSwitch(action);
        break;
      default:
        console.error("Unknown action type:", action.type);
    }
  }

  // Execute a move action
  async executeMove(action) {
    const { pokemon, move, moveIndex, target } = action;

    // Use the move (decreases PP and handles confusion)
    const moveResult = pokemon.useMove(moveIndex);

    if (moveResult && moveResult.type === "confusion_self_damage") {
      await this.showMessage(`${pokemon.name} is confused and hurt itself!`);
      this.updatePokemonDisplay(
        pokemon === this.currentPlayerPokemon ? "player" : "enemy",
        pokemon
      );
      return;
    }

    if (!moveResult) {
      await this.showMessage(`${pokemon.name} couldn't use ${move.name}!`);
      return;
    }

    await this.showMessage(`${pokemon.name} used ${move.name}!`);

    // Check accuracy
    const accuracy = move.accuracy || 100;
    const hitChance = Math.random() * 100;

    if (hitChance > accuracy) {
      await this.showMessage(`${pokemon.name}'s attack missed!`);
      // Show "Missed" using existing damage animation system
      const targetSide =
        target === this.currentPlayerPokemon ? "player" : "enemy";
      if (typeof uiManager !== "undefined") {
        await uiManager.animateMiss(targetSide);
      }
      return;
    }

    // Calculate and apply damage
    // Check if this is a damage move (has power or is a special variable power move)
    const variablePowerMoves = [
      "electro-ball",
      "gyro-ball",
      "grass-knot",
      "low-kick",
      "heavy-slam",
      "heat-crash",
      "seismic-toss",
      "night-shade",
    ];
    const isDamageMove =
      (move.power && move.power > 0) || variablePowerMoves.includes(move.name);

    if (isDamageMove) {
      const damage = calculateDamage(pokemon, target, move);
      const actualDamage = target.takeDamage(damage);

      await this.showMessage(`It dealt ${actualDamage} damage!`);

      // Update target display with animation
      const targetSide =
        target === this.currentPlayerPokemon ? "player" : "enemy";

      // Add damage animation if UI manager is available
      if (typeof uiManager !== "undefined") {
        await uiManager.animateDamage(targetSide, actualDamage);
      }

      await this.updateHPBar(targetSide, target, true);

      // Check for type effectiveness messages
      const effectiveness = getTypeEffectiveness(move.type, target.types);
      if (effectiveness > 1) {
        await this.showMessage("It's super effective!", 1000);
      } else if (effectiveness < 1 && effectiveness > 0) {
        await this.showMessage("It's not very effective...", 1000);
      } else if (effectiveness === 0) {
        await this.showMessage("It had no effect!", 1000);
      }

      // Check if target fainted
      if (target.fainted) {
        const targetSide =
          target === this.currentPlayerPokemon ? "player" : "enemy";
        await this.handlePokemonFainted(target, targetSide);
      }
    } else {
      // Status move
      await this.showMessage(`${pokemon.name} used ${move.name}!`);
      // TODO: Implement status move effects
    }
  }

  // Execute a switch action
  async executeSwitch(action) {
    const { pokemon, newPokemon } = action;

    if (pokemon === this.currentPlayerPokemon) {
      await this.showMessage(`Come back, ${pokemon.name}!`);
      this.currentPlayerPokemon = newPokemon;
      await this.showMessage(`Go! ${newPokemon.name}!`);
      this.updatePokemonDisplay("player", newPokemon);
    } else {
      await this.showMessage(`${this.trainer.name} withdrew ${pokemon.name}!`);
      this.currentEnemyPokemon = newPokemon;
      await this.showMessage(
        `${this.trainer.name} sent out ${newPokemon.name}!`
      );
      this.updatePokemonDisplay("enemy", newPokemon);
    }
  }

  // End turn processing
  async endTurn() {
    // Clear turn queue
    this.turnQueue = [];

    // Process end-of-turn effects for both Pokemon
    const playerEffects = this.currentPlayerPokemon.processEndOfTurn();
    const enemyEffects = this.currentEnemyPokemon.processEndOfTurn();

    // Show status damage messages
    for (const effect of [...playerEffects, ...enemyEffects]) {
      if (effect.type === "status_damage") {
        await this.showMessage(
          `${effect.pokemon} is hurt by ${effect.status}!`,
          1000
        );
        const side =
          effect.pokemon === this.currentPlayerPokemon.name
            ? "player"
            : "enemy";
        const pokemon =
          effect.pokemon === this.currentPlayerPokemon.name
            ? this.currentPlayerPokemon
            : this.currentEnemyPokemon;
        this.updatePokemonDisplay(side, pokemon);

        if (pokemon.fainted) {
          const side =
            pokemon === this.currentPlayerPokemon ? "player" : "enemy";
          await this.handlePokemonFainted(pokemon, side);
        }
      } else if (effect.type === "status_cleared") {
        await this.showMessage(
          `${effect.pokemon} recovered from ${effect.status}!`,
          1000
        );
      }
    }

    // Check if battle should end
    if (this.checkBattleEnd()) {
      return;
    }

    // Start next turn
    this.battleState = "selectAction";
    this.showActionMenu();
    this.showMessage(`What will ${this.currentPlayerPokemon.name} do?`);
  }

  // Check if battle should end
  checkBattleEnd() {
    const playerAlive = this.playerTeam.some((p) => !p.fainted);
    const enemyAlive = this.enemyTeam.some((p) => !p.fainted);

    if (!playerAlive) {
      this.endBattle("enemy");
      return true;
    } else if (!enemyAlive) {
      this.endBattle("player");
      return true;
    }

    return false;
  }

  // Handle Pokemon fainting
  async handlePokemonFainted(pokemon, side) {
    await this.showMessage(`${pokemon.name} fainted!`);

    // Add fainting animation if UI manager is available
    if (typeof uiManager !== "undefined") {
      const sprite = document.getElementById(`${side}-sprite`);
      sprite.classList.add("fainted");

      // Remove the animation class after animation completes
      setTimeout(() => {
        sprite.classList.remove("fainted");
      }, 1000);
    }

    // Check if battle is over
    if (this.checkBattleEnd()) {
      return;
    }

    // Force switch if current Pokemon fainted
    if (side === "player" && pokemon === this.currentPlayerPokemon) {
      await this.showMessage("Choose your next Pokemon!");
      this.showPokemonMenu(true); // Force switch
    } else if (side === "enemy" && pokemon === this.currentEnemyPokemon) {
      // AI switches to next Pokemon
      const nextPokemon = this.enemyTeam.find((p) => !p.fainted);
      if (nextPokemon) {
        this.currentEnemyPokemon = nextPokemon;
        await this.showMessage(
          `${this.trainer.name} sent out ${nextPokemon.name}!`
        );
        this.updatePokemonDisplay("enemy", nextPokemon);
      }
    }
  }

  // End battle
  async endBattle(winner) {
    this.battleState = "end";
    this.winner = winner;
    this.hideAllMenus();

    if (winner === "player") {
      await this.showMessage(`You defeated ${this.trainer.name}!`);
    } else {
      await this.showMessage(`You were defeated by ${this.trainer.name}!`);
    }

    // Notify game controller
    if (window.gameController) {
      setTimeout(() => {
        window.gameController.onBattleEnd({ winner: winner });
      }, 2000);
    }
  }

  // Handle keyboard input
  handleKeyPress(event) {
    // Basic keyboard navigation
    switch (event.key) {
      case "Escape":
        if (
          this.battleState === "selectMove" ||
          this.battleState === "selectPokemon" ||
          this.battleState === "selectItem"
        ) {
          this.showActionMenu();
        }
        break;
    }
  }
}
