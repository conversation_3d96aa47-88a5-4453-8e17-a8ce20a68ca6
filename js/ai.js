// Pokemon AI System

class PokemonAI {
  constructor(difficulty = "BASIC") {
    this.difficulty = difficulty;
    this.config = CONFIG.AI[difficulty];
    console.log(`AI initialized with difficulty: ${difficulty}`);
  }

  // Select action for AI Pokemon
  selectAction(aiPokemon, targetPokemon, aiTeam) {
    switch (this.difficulty) {
      case "BASIC":
        return this.selectBasicAction(aiPokemon, targetPokemon);
      case "STANDARD":
        return this.selectStandardAction(aiPokemon, targetPokemon);
      case "EXPERT":
        return this.selectExpertAction(aiPokemon, targetPokemon, aiTeam);
      default:
        return this.selectBasicAction(aiPokemon, targetPokemon);
    }
  }

  // Basic AI: Random move selection
  selectBasicAction(aiPokemon, targetPokemon) {
    // Simple random move selection
    const availableMoves = aiPokemon.moves.filter((move, index) =>
      aiPokemon.canUseMove(index)
    );

    if (availableMoves.length === 0) {
      // No moves available, use struggle (not implemented, use first move)
      return this.createMoveAction(aiPokemon, targetPokemon, 0);
    }

    const randomMoveIndex = aiPokemon.moves.indexOf(
      availableMoves[Math.floor(Math.random() * availableMoves.length)]
    );

    return this.createMoveAction(aiPokemon, targetPokemon, randomMoveIndex);
  }

  // Standard AI: Type effectiveness consideration
  selectStandardAction(aiPokemon, targetPokemon) {
    const moveScores = [];

    // Evaluate each move
    aiPokemon.moves.forEach((move, index) => {
      if (!aiPokemon.canUseMove(index)) {
        moveScores.push({ index, score: -1 });
        return;
      }

      let score = move.power || 50; // Base score

      // Type effectiveness bonus
      const effectiveness = getTypeEffectiveness(
        move.type,
        targetPokemon.types
      );
      score *= effectiveness * this.config.typeEffectivenessWeight;

      // STAB bonus
      if (aiPokemon.types.includes(move.type)) {
        score *= CONFIG.BATTLE.STAB_MULTIPLIER;
      }

      // Prefer moves with higher accuracy
      score *= (move.accuracy || 100) / 100;

      // Random factor
      score *= 0.8 + Math.random() * 0.4;

      moveScores.push({ index, score });
    });

    // Select best move
    const bestMove = moveScores.reduce((best, current) =>
      current.score > best.score ? current : best
    );

    return this.createMoveAction(aiPokemon, targetPokemon, bestMove.index);
  }

  // Expert AI: Strategic decisions
  selectExpertAction(aiPokemon, targetPokemon, aiTeam) {
    // Check if should switch Pokemon
    if (this.shouldSwitch(aiPokemon, targetPokemon, aiTeam)) {
      const bestSwitch = this.selectBestSwitch(
        aiPokemon,
        targetPokemon,
        aiTeam
      );
      if (bestSwitch) {
        return this.createSwitchAction(aiPokemon, bestSwitch);
      }
    }

    // Advanced move selection
    const moveScores = [];

    aiPokemon.moves.forEach((move, index) => {
      if (!aiPokemon.canUseMove(index)) {
        moveScores.push({ index, score: -1 });
        return;
      }

      let score = this.evaluateMove(move, aiPokemon, targetPokemon);
      moveScores.push({ index, score });
    });

    // Select best move
    const bestMove = moveScores.reduce((best, current) =>
      current.score > best.score ? current : best
    );

    return this.createMoveAction(aiPokemon, targetPokemon, bestMove.index);
  }

  // Evaluate move effectiveness for expert AI
  evaluateMove(move, aiPokemon, targetPokemon) {
    let score = move.power || 50;

    // Type effectiveness
    const effectiveness = getTypeEffectiveness(move.type, targetPokemon.types);
    score *= effectiveness * this.config.typeEffectivenessWeight;

    // STAB bonus
    if (aiPokemon.types.includes(move.type)) {
      score *= CONFIG.BATTLE.STAB_MULTIPLIER;
    }

    // Accuracy consideration
    score *= (move.accuracy || 100) / 100;

    // Priority move bonus
    if (move.priority && move.priority > 0) {
      score *= this.config.priorityMoveWeight || 1.2;
    }

    // Damage calculation consideration
    if (this.config.damageCalculation && move.power) {
      const estimatedDamage = calculateDamage(aiPokemon, targetPokemon, move);
      const damageRatio = estimatedDamage / targetPokemon.currentHP;

      // Bonus for moves that can KO
      if (damageRatio >= 1) {
        score *= 2;
      } else if (damageRatio > 0.5) {
        score *= 1.5;
      }
    }

    // Status move consideration
    if (!move.power && Math.random() < this.config.statusMoveChance) {
      score = 30; // Base score for status moves
    }

    // Random factor (smaller for expert AI)
    score *= 0.9 + Math.random() * 0.2;

    return score;
  }

  // Check if AI should switch Pokemon
  shouldSwitch(aiPokemon, targetPokemon, aiTeam) {
    // Don't switch if this is the last Pokemon
    const alivePokemon = aiTeam.filter((p) => !p.fainted);
    if (alivePokemon.length <= 1) {
      return false;
    }

    // Check type disadvantage
    const hasTypeDisadvantage = targetPokemon.types.some((targetType) => {
      return aiPokemon.types.some((aiType) => {
        const effectiveness = getTypeEffectiveness(targetType, [aiType]);
        return effectiveness > 1;
      });
    });

    // Switch if at low HP and type disadvantaged
    if (aiPokemon.getHPPercentage() < 30 && hasTypeDisadvantage) {
      return Math.random() < this.config.switchChance;
    }

    // Random switch chance
    return Math.random() < this.config.switchChance * 0.3;
  }

  // Select best Pokemon to switch to
  selectBestSwitch(currentPokemon, targetPokemon, aiTeam) {
    const availablePokemon = aiTeam.filter(
      (p) => !p.fainted && p !== currentPokemon
    );

    if (availablePokemon.length === 0) {
      return null;
    }

    // Score each Pokemon
    const pokemonScores = availablePokemon.map((pokemon) => {
      let score = pokemon.getHPPercentage(); // Prefer healthy Pokemon

      // Type advantage bonus
      const hasTypeAdvantage = pokemon.types.some((pokemonType) => {
        return targetPokemon.types.some((targetType) => {
          const effectiveness = getTypeEffectiveness(pokemonType, [targetType]);
          return effectiveness > 1;
        });
      });

      if (hasTypeAdvantage) {
        score += 50;
      }

      // Type resistance bonus
      const hasTypeResistance = targetPokemon.types.some((targetType) => {
        return pokemon.types.some((pokemonType) => {
          const effectiveness = getTypeEffectiveness(targetType, [pokemonType]);
          return effectiveness < 1;
        });
      });

      if (hasTypeResistance) {
        score += 30;
      }

      return { pokemon, score };
    });

    // Return best Pokemon
    const bestSwitch = pokemonScores.reduce((best, current) =>
      current.score > best.score ? current : best
    );

    return bestSwitch.pokemon;
  }

  // Create move action
  createMoveAction(pokemon, target, moveIndex) {
    const move = pokemon.moves[moveIndex];
    return {
      type: "move",
      pokemon: pokemon,
      move: move,
      moveIndex: moveIndex,
      target: target,
      priority: move.priority || 0,
      speed: pokemon.stats.speed,
    };
  }

  // Create switch action
  createSwitchAction(currentPokemon, newPokemon) {
    return {
      type: "switch",
      pokemon: currentPokemon,
      newPokemon: newPokemon,
      priority: 6, // Switching has high priority
      speed: 999,
    };
  }
}

console.log("AI System module loaded");
