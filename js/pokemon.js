// Pokemon Class and Battle-related functionality

class Pokemon {
  constructor(data, level = CONFIG.BATTLE.DEFAULT_LEVEL) {
    this.id = data.id;
    this.name = data.name;
    this.types = data.types;
    this.level = level;
    this.baseStats = data.baseStats;
    this.sprites = data.sprites;
    this.height = data.height;
    this.weight = data.weight;

    // Calculate actual stats based on level
    this.stats = this.calculateStats();

    // Current battle stats
    this.currentHP = this.stats.hp;
    this.maxHP = this.stats.hp;

    // Status conditions
    this.status = null; // poison, burn, paralysis, sleep, freeze
    this.statusTurns = 0;
    this.confusion = false;
    this.confusionTurns = 0;

    // Moves (select up to 4 from available moves)
    // Check if moves are already level-appropriate (have learnedAtLevel property)
    const hasLevelAppropriateMoves =
      data.moves &&
      data.moves.length > 0 &&
      data.moves[0].learnedAtLevel !== undefined;

    if (hasLevelAppropriateMoves) {
      // Use level-appropriate moves directly, just add currentPP
      this.moves = data.moves.map((move) => ({
        ...move,
        currentPP: move.pp,
      }));
      console.log(
        `${this.name} level ${this.level} moves:`,
        this.moves.map((m) => `${m.name} (learned at ${m.learnedAtLevel})`)
      );
    } else {
      // Apply filtering for raw API moves
      this.moves = this.selectMoves(data.moves);
    }

    // Battle state
    this.fainted = false;
    this.switched = false;
  }

  // Calculate actual stats based on level and base stats
  calculateStats() {
    const stats = {};

    // HP calculation: ((Base + IV + EV/4) * Level / 50) + Level + 10
    // Simplified: assume IV=31, EV=0 for all stats
    stats.hp = Math.floor(
      ((this.baseStats.hp + 31) * this.level) / 50 + this.level + 10
    );

    // Other stats: ((Base + IV + EV/4) * Level / 50) + 5
    stats.attack = Math.floor(
      ((this.baseStats.attack + 31) * this.level) / 50 + 5
    );
    stats.defense = Math.floor(
      ((this.baseStats.defense + 31) * this.level) / 50 + 5
    );
    stats.specialAttack = Math.floor(
      ((this.baseStats.specialAttack + 31) * this.level) / 50 + 5
    );
    stats.specialDefense = Math.floor(
      ((this.baseStats.specialDefense + 31) * this.level) / 50 + 5
    );
    stats.speed = Math.floor(
      ((this.baseStats.speed + 31) * this.level) / 50 + 5
    );

    return stats;
  }

  // Select moves for this Pokemon (up to 4)
  selectMoves(availableMoves) {
    if (!availableMoves || availableMoves.length === 0) {
      // Default move if no moves available
      return [
        {
          name: "tackle",
          power: 40,
          accuracy: 100,
          pp: 35,
          currentPP: 35,
          type: "normal",
          damageClass: "physical",
          description:
            "A physical attack in which the user charges and slams into the target with its whole body.",
        },
      ];
    }

    // Filter out status moves with no power for basic implementation
    const damageMovesFirst = availableMoves.filter(
      (move) => move.power && move.power > 0
    );
    const statusMoves = availableMoves.filter(
      (move) => !move.power || move.power === 0
    );

    // Prioritize damage moves, then add some status moves
    const selectedMoves = [];

    // Add up to 3 damage moves
    for (let i = 0; i < Math.min(3, damageMovesFirst.length); i++) {
      const move = { ...damageMovesFirst[i] };
      move.currentPP = move.pp;
      selectedMoves.push(move);
    }

    // Add 1 status move if available and we have room
    if (selectedMoves.length < 4 && statusMoves.length > 0) {
      const move = { ...statusMoves[0] };
      move.currentPP = move.pp;
      selectedMoves.push(move);
    }

    // Fill remaining slots with more damage moves if available
    while (
      selectedMoves.length < 4 &&
      selectedMoves.length < damageMovesFirst.length
    ) {
      const move = { ...damageMovesFirst[selectedMoves.length] };
      move.currentPP = move.pp;
      selectedMoves.push(move);
    }

    return selectedMoves;
  }

  // Take damage
  takeDamage(damage) {
    this.currentHP = Math.max(0, this.currentHP - damage);
    if (this.currentHP === 0) {
      this.fainted = true;
      this.status = null; // Clear status when fainted
      this.confusion = false;
    }
    return damage;
  }

  // Heal HP
  heal(amount) {
    if (this.fainted) return 0;

    const oldHP = this.currentHP;
    this.currentHP = Math.min(this.maxHP, this.currentHP + amount);
    return this.currentHP - oldHP;
  }

  // Set status condition
  setStatus(status) {
    if (this.fainted || this.status) return false; // Can't set status if already has one or fainted

    this.status = status;
    this.statusTurns = 0;

    // Set duration for certain status conditions
    if (status === "sleep") {
      this.statusTurns = Math.floor(Math.random() * 3) + 1; // 1-3 turns
    } else if (status === "freeze") {
      this.statusTurns = Math.floor(Math.random() * 4) + 1; // 1-4 turns
    }

    return true;
  }

  // Clear status condition
  clearStatus() {
    this.status = null;
    this.statusTurns = 0;
  }

  // Set confusion
  setConfusion() {
    if (this.fainted) return false;

    this.confusion = true;
    this.confusionTurns = Math.floor(Math.random() * 4) + 2; // 2-5 turns
    return true;
  }

  // Clear confusion
  clearConfusion() {
    this.confusion = false;
    this.confusionTurns = 0;
  }

  // Process end-of-turn effects
  processEndOfTurn() {
    const effects = [];

    // Process status damage
    if (this.status === "poison" || this.status === "burn") {
      const damage = Math.floor(
        this.maxHP * CONFIG.BATTLE.STATUS_DAMAGE.POISON
      );
      this.takeDamage(damage);
      effects.push({
        type: "status_damage",
        status: this.status,
        damage: damage,
        pokemon: this.name,
      });
    }

    // Process status turn countdown
    if (this.status === "sleep" || this.status === "freeze") {
      this.statusTurns--;
      if (this.statusTurns <= 0) {
        const clearedStatus = this.status;
        this.clearStatus();
        effects.push({
          type: "status_cleared",
          status: clearedStatus,
          pokemon: this.name,
        });
      }
    }

    // Process confusion
    if (this.confusion) {
      this.confusionTurns--;
      if (this.confusionTurns <= 0) {
        this.clearConfusion();
        effects.push({
          type: "confusion_cleared",
          pokemon: this.name,
        });
      }
    }

    return effects;
  }

  // Check if Pokemon can use a move
  canUseMove(moveIndex) {
    if (this.fainted) return false;
    if (moveIndex < 0 || moveIndex >= this.moves.length) return false;

    const move = this.moves[moveIndex];
    if (move.currentPP <= 0) return false;

    // Check status conditions that prevent moves
    if (this.status === "sleep" || this.status === "freeze") return false;
    if (this.status === "paralysis" && Math.random() < 0.25) return false; // 25% chance to be paralyzed

    return true;
  }

  // Use a move (decreases PP)
  useMove(moveIndex) {
    if (!this.canUseMove(moveIndex)) return null;

    const move = this.moves[moveIndex];
    move.currentPP--;

    // Check for confusion self-damage
    if (this.confusion && Math.random() < 0.5) {
      const confusionDamage = Math.floor(this.maxHP * 0.1); // 10% self-damage
      this.takeDamage(confusionDamage);
      return {
        type: "confusion_self_damage",
        damage: confusionDamage,
        pokemon: this.name,
      };
    }

    return move;
  }

  // Get HP percentage
  getHPPercentage() {
    return this.maxHP > 0 ? (this.currentHP / this.maxHP) * 100 : 0;
  }

  // Check if Pokemon is at low HP
  isLowHP() {
    return this.getHPPercentage() < 25;
  }

  // Check if Pokemon is at critical HP
  isCriticalHP() {
    return this.getHPPercentage() < 10;
  }

  // Get display name with level
  getDisplayName() {
    return `${this.name.charAt(0).toUpperCase() + this.name.slice(1)} (Lv.${
      this.level
    })`;
  }

  // Get status display text
  getStatusText() {
    const statusTexts = [];

    if (this.status) {
      statusTexts.push(this.status.toUpperCase());
    }

    if (this.confusion) {
      statusTexts.push("CONFUSED");
    }

    return statusTexts.join(", ");
  }

  // Reset Pokemon to full health (for new battles)
  reset() {
    this.currentHP = this.maxHP;
    this.fainted = false;
    this.status = null;
    this.statusTurns = 0;
    this.confusion = false;
    this.confusionTurns = 0;
    this.switched = false;

    // Reset move PP
    this.moves.forEach((move) => {
      move.currentPP = move.pp;
    });
  }

  // Create a copy of this Pokemon
  clone() {
    const pokemonData = {
      id: this.id,
      name: this.name,
      types: [...this.types],
      baseStats: { ...this.baseStats },
      sprites: { ...this.sprites },
      height: this.height,
      weight: this.weight,
      moves: this.moves.map((move) => ({ ...move })),
    };

    return new Pokemon(pokemonData, this.level);
  }
}

// Evolution logic functions
async function getEvolutionChain(pokemonId) {
  try {
    const speciesData = await pokemonAPI.fetchPokemonSpecies(pokemonId);
    const evolutionChain = await pokemonAPI.fetchEvolutionChain(
      speciesData.evolution_chain.url
    );
    return evolutionChain;
  } catch (error) {
    console.error(`Error fetching evolution chain for ${pokemonId}:`, error);
    return null;
  }
}

// Determine what Pokemon should be at a given level
async function getCorrectPokemonForm(baseId, level) {
  try {
    const evolutionChain = await getEvolutionChain(baseId);
    if (!evolutionChain) {
      return baseId; // Return original if can't get evolution data
    }

    // Parse evolution chain to find correct form
    const evolutionPath = parseEvolutionChain(evolutionChain.chain);

    // Find the correct evolution based on level
    for (let i = evolutionPath.length - 1; i >= 0; i--) {
      const evolution = evolutionPath[i];
      if (level >= evolution.minLevel) {
        console.log(
          `Level ${level} ${evolution.name} (evolves at level ${evolution.minLevel})`
        );
        return evolution.id;
      }
    }

    // If no evolution found, return the base form
    return evolutionPath[0].id;
  } catch (error) {
    console.error(
      `Error determining correct form for ${baseId} at level ${level}:`,
      error
    );
    return baseId;
  }
}

// Parse evolution chain into a linear path (for level-based evolutions)
function parseEvolutionChain(chain) {
  const evolutionPath = [];

  function traverseChain(currentChain, currentLevel = 1) {
    // Add current Pokemon to path
    const pokemonId = extractPokemonIdFromUrl(currentChain.species.url);
    evolutionPath.push({
      name: currentChain.species.name,
      id: pokemonId,
      minLevel: currentLevel,
    });

    // Process evolutions
    if (currentChain.evolves_to && currentChain.evolves_to.length > 0) {
      // For simplicity, take the first evolution path
      const nextEvolution = currentChain.evolves_to[0];

      // Check if it's a level-based evolution
      const levelTrigger = nextEvolution.evolution_details.find(
        (detail) => detail.trigger.name === "level-up" && detail.min_level
      );

      if (levelTrigger) {
        traverseChain(nextEvolution, levelTrigger.min_level);
      } else {
        // For non-level evolutions, assume they can happen at any level above base
        traverseChain(nextEvolution, currentLevel);
      }
    }
  }

  traverseChain(chain);
  return evolutionPath;
}

// Extract Pokemon ID from species URL
function extractPokemonIdFromUrl(url) {
  const matches = url.match(/\/(\d+)\/$/);
  return matches ? parseInt(matches[1]) : null;
}

// Get level-appropriate moves for a Pokemon
async function getLevelAppropriateMoves(pokemonId, level) {
  try {
    const levelUpMoves = await pokemonAPI.fetchLevelUpMoves(pokemonId);

    // Filter moves that can be learned at or below the current level
    const availableMoves = levelUpMoves.filter((move) => move.level <= level);

    if (availableMoves.length === 0) {
      console.log(
        `No level-up moves found for Pokemon ${pokemonId} at level ${level}`
      );
      return [];
    }

    // Get the most recent 4 unique moves (Pokemon can only know 4 moves)
    const recentMoves = availableMoves.slice(-4);

    console.log(
      `Pokemon ${pokemonId} level ${level} - Available moves:`,
      availableMoves.map((m) => `${m.move} (level ${m.level})`)
    );
    console.log(
      `Selected moves:`,
      recentMoves.map((m) => `${m.move} (level ${m.level})`)
    );

    // Fetch detailed move data
    const movePromises = recentMoves.map(async (moveEntry) => {
      try {
        const moveData = await pokemonAPI.queueRequest(moveEntry.moveUrl);
        return {
          name: moveData.name,
          power: moveData.power,
          accuracy: moveData.accuracy,
          pp: moveData.pp,
          type: moveData.type.name,
          damageClass: moveData.damage_class.name,
          description:
            moveData.effect_entries.find(
              (entry) => entry.language.name === "en"
            )?.short_effect || "No description available",
          learnedAtLevel: moveEntry.level,
        };
      } catch (error) {
        console.error(`Error loading move ${moveEntry.move}:`, error);
        return null;
      }
    });

    const resolvedMoves = await Promise.all(movePromises);
    const validMoves = resolvedMoves.filter((move) => move !== null);

    // Ensure we have unique moves (final safety check)
    const uniqueMoves = [];
    const seenMoves = new Set();

    for (const move of validMoves) {
      if (!seenMoves.has(move.name)) {
        seenMoves.add(move.name);
        uniqueMoves.push(move);
      }
    }

    console.log(
      `Final unique moves:`,
      uniqueMoves.map((m) => m.name)
    );
    return uniqueMoves;
  } catch (error) {
    console.error(
      `Error getting level-appropriate moves for ${pokemonId} at level ${level}:`,
      error
    );
    return [];
  }
}

// Enhanced utility function to create Pokemon from API data with evolution and level-appropriate moves
async function createPokemonFromId(id, level = CONFIG.BATTLE.DEFAULT_LEVEL) {
  try {
    // Determine the correct evolved form for this level
    const correctFormId = await getCorrectPokemonForm(id, level);

    // Fetch the Pokemon data for the correct form
    const pokemonData = await pokemonAPI.fetchPokemon(correctFormId);

    // Get level-appropriate moves
    const levelMoves = await getLevelAppropriateMoves(correctFormId, level);

    // Replace the moves with level-appropriate ones
    if (levelMoves.length > 0) {
      pokemonData.moves = levelMoves;
    }

    console.log(
      `Created ${pokemonData.name} at level ${level} with moves:`,
      levelMoves.map((m) => `${m.name} (learned at ${m.learnedAtLevel})`)
    );

    return new Pokemon(pokemonData, level);
  } catch (error) {
    console.error(`Error creating Pokemon with ID ${id}:`, error);
    return null;
  }
}

// Utility function to create a team of Pokemon
async function createPokemonTeam(teamConfig) {
  console.log(`Creating team of ${teamConfig.length} Pokemon...`);

  // Create all Pokemon in parallel for faster loading
  const pokemonPromises = teamConfig.map((config) =>
    createPokemonFromId(config.id, config.level)
  );

  const pokemonResults = await Promise.all(pokemonPromises);

  // Filter out any null results
  const team = pokemonResults.filter((pokemon) => pokemon !== null);

  console.log(
    `Team created: ${team.length}/${teamConfig.length} Pokemon loaded successfully`
  );
  return team;
}
