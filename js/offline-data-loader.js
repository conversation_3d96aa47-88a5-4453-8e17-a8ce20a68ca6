/**
 * Offline Data Loader Module
 * Loads Pokemon data from local JSON files with fallback to API
 */

class OfflineDataLoader {
  constructor() {
    this.dataPath = './data';
    this.cache = new Map();
    this.isOnline = navigator.onLine;
    
    // Listen for online/offline events
    window.addEventListener('online', () => {
      this.isOnline = true;
      console.log('Connection restored - API fallback available');
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
      console.log('Connection lost - using offline data only');
    });
  }

  /**
   * Load JSON data from a local file
   */
  async loadLocalFile(filePath) {
    const cacheKey = `local_${filePath}`;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(filePath);
      if (!response.ok) {
        throw new Error(`Failed to load ${filePath}: ${response.status}`);
      }
      
      const data = await response.json();
      this.cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.warn(`Could not load local file ${filePath}:`, error.message);
      return null;
    }
  }

  /**
   * Load Pokemon data by ID
   */
  async loadPokemon(id) {
    const paddedId = id.toString().padStart(3, '0');
    
    try {
      // Try to load from local data first
      const pokemonFiles = await this.loadLocalFile(`${this.dataPath}/metadata/pokemon-index.json`);
      
      if (pokemonFiles) {
        const pokemonFile = pokemonFiles.find(file => file.id === id);
        if (pokemonFile) {
          const pokemonData = await this.loadLocalFile(`${this.dataPath}/pokemon/${pokemonFile.filename}`);
          if (pokemonData) {
            console.log(`Loaded Pokemon ${id} from offline data`);
            return pokemonData;
          }
        }
      }

      // Fallback: try to find file by pattern
      const possibleFiles = [
        `${this.dataPath}/pokemon/${paddedId}-bulbasaur.json`,
        `${this.dataPath}/pokemon/${paddedId}-ivysaur.json`,
        `${this.dataPath}/pokemon/${paddedId}-venusaur.json`,
        // Add more common patterns as needed
      ];

      for (const filePath of possibleFiles) {
        const data = await this.loadLocalFile(filePath);
        if (data && data.id === id) {
          console.log(`Loaded Pokemon ${id} from offline data (pattern match)`);
          return data;
        }
      }

      throw new Error(`Pokemon ${id} not found in offline data`);
    } catch (error) {
      console.warn(`Failed to load Pokemon ${id} from offline data:`, error.message);
      return null;
    }
  }

  /**
   * Load Pokemon species data by ID
   */
  async loadSpecies(id) {
    const paddedId = id.toString().padStart(3, '0');
    
    try {
      // Try to load from local data
      const speciesFiles = await this.loadLocalFile(`${this.dataPath}/metadata/species-index.json`);
      
      if (speciesFiles) {
        const speciesFile = speciesFiles.find(file => file.id === id);
        if (speciesFile) {
          const speciesData = await this.loadLocalFile(`${this.dataPath}/species/${speciesFile.filename}`);
          if (speciesData) {
            console.log(`Loaded Species ${id} from offline data`);
            return speciesData;
          }
        }
      }

      throw new Error(`Species ${id} not found in offline data`);
    } catch (error) {
      console.warn(`Failed to load Species ${id} from offline data:`, error.message);
      return null;
    }
  }

  /**
   * Load evolution chain data by chain ID
   */
  async loadEvolutionChain(chainId) {
    try {
      const chainData = await this.loadLocalFile(`${this.dataPath}/evolution-chains/chain-${chainId}.json`);
      if (chainData) {
        console.log(`Loaded Evolution Chain ${chainId} from offline data`);
        return chainData;
      }
      
      throw new Error(`Evolution chain ${chainId} not found in offline data`);
    } catch (error) {
      console.warn(`Failed to load Evolution Chain ${chainId} from offline data:`, error.message);
      return null;
    }
  }

  /**
   * Load evolution chain data by URL
   */
  async loadEvolutionChainByUrl(chainUrl) {
    const chainId = chainUrl.split('/').slice(-2, -1)[0];
    return this.loadEvolutionChain(chainId);
  }

  /**
   * Load move data by name
   */
  async loadMove(moveName) {
    const normalizedName = moveName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    
    try {
      const moveData = await this.loadLocalFile(`${this.dataPath}/moves/${normalizedName}.json`);
      if (moveData) {
        console.log(`Loaded Move ${moveName} from offline data`);
        return moveData;
      }
      
      throw new Error(`Move ${moveName} not found in offline data`);
    } catch (error) {
      console.warn(`Failed to load Move ${moveName} from offline data:`, error.message);
      return null;
    }
  }

  /**
   * Load move data by URL
   */
  async loadMoveByUrl(moveUrl) {
    const moveName = moveUrl.split('/').slice(-2, -1)[0];
    return this.loadMove(moveName);
  }

  /**
   * Check if offline data is available
   */
  async checkOfflineDataAvailability() {
    try {
      const metadata = await this.loadLocalFile(`${this.dataPath}/metadata/download-info.json`);
      if (metadata) {
        console.log(`Offline data available: ${metadata.pokemonCount} Pokemon, ${metadata.movesCount} moves`);
        console.log(`Data downloaded: ${metadata.downloadDate}`);
        return true;
      }
      return false;
    } catch (error) {
      console.warn('No offline data metadata found');
      return false;
    }
  }

  /**
   * Get offline data statistics
   */
  async getOfflineDataStats() {
    try {
      const metadata = await this.loadLocalFile(`${this.dataPath}/metadata/download-info.json`);
      return metadata || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Clear local cache
   */
  clearCache() {
    this.cache.clear();
    console.log('Offline data cache cleared');
  }

  /**
   * Check if we're in offline mode
   */
  isOfflineMode() {
    return !this.isOnline;
  }

  /**
   * Generate index files for faster lookups (utility function)
   */
  async generateIndexFiles() {
    console.log('This function should be run server-side to generate index files');
    // This would be implemented as a separate Node.js script
    // that scans the data directory and creates index files
  }
}

// Create global offline data loader instance
const offlineDataLoader = new OfflineDataLoader();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = OfflineDataLoader;
}
