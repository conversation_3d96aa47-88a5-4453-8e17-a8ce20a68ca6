// Main Game Controller

class GameController {
  constructor() {
    this.currentScreen = "loading";
    this.gameState = "menu";
    this.playerTeam = [];
    this.selectedTrainer = null;
    this.battleSystem = null;

    this.init();
  }

  async init() {
    console.log("Initializing Pokemon Battle Game...");

    // Show loading screen
    this.showScreen("loading");
    this.updateLoadingText("Initializing game...");

    try {
      // Test basic functionality first
      this.updateLoadingText("Testing API connection...");
      console.log("Testing PokeAPI connection...");

      // Try to fetch a single Pokemon first
      const testPokemon = await pokemonAPI.fetchPokemon(25); // Pikachu
      console.log("Test Pokemon loaded:", testPokemon);

      // Preload essential Pokemon data
      this.updateLoadingText("Loading Pokemon data...");
      console.log("Preloading essential Pokemon...");
      await pokemonAPI.preloadEssentialPokemon();

      // Create player team
      this.updateLoadingText("Creating your team...");
      console.log("Creating player team...");
      this.playerTeam = await createPokemonTeam(CONFIG.PLAYER_TEAM);
      console.log("Player team created:", this.playerTeam.length, "Pokemon");

      // Start background loading of all Pokemon
      this.updateLoadingText("Loading additional Pokemon...");
      this.startBackgroundLoading();

      // Setup event listeners
      this.setupEventListeners();

      // Show main menu
      this.updateLoadingText("Ready to battle!");
      setTimeout(() => {
        this.showScreen("main-menu");
        this.gameState = "menu";
      }, 1000);
    } catch (error) {
      console.error("Error initializing game:", error);
      this.updateLoadingText(
        `Error: ${error.message}. Check console for details.`
      );

      // Show a simplified menu after error for testing
      setTimeout(() => {
        this.setupEventListeners();
        this.showScreen("main-menu");
        this.gameState = "menu";
      }, 3000);
    }
  }

  // Start background loading of all Pokemon
  startBackgroundLoading() {
    pokemonAPI
      .loadAllFirstGenPokemon((progress) => {
        console.log(
          `Loading progress: ${progress.percentage}% (${progress.loaded}/${progress.total})`
        );
      })
      .catch((error) => {
        console.error("Background loading error:", error);
      });
  }

  // Setup event listeners
  setupEventListeners() {
    // Main menu buttons
    document
      .getElementById("start-battle-btn")
      .addEventListener("click", () => {
        this.showScreen("trainer-selection");
      });

    document
      .getElementById("select-trainer-btn")
      .addEventListener("click", () => {
        this.showScreen("trainer-selection");
      });

    // Trainer selection
    document.querySelectorAll(".trainer-card").forEach((card) => {
      card.addEventListener("click", () => {
        const difficulty = card.dataset.difficulty;
        this.selectTrainer(difficulty);
      });
    });

    document
      .getElementById("back-to-menu-btn")
      .addEventListener("click", () => {
        this.showScreen("main-menu");
      });

    // Battle result buttons
    document
      .getElementById("battle-again-btn")
      .addEventListener("click", () => {
        this.startBattle();
      });

    document.getElementById("return-menu-btn").addEventListener("click", () => {
      this.showScreen("main-menu");
    });

    // Keyboard controls
    document.addEventListener("keydown", (event) => {
      this.handleKeyPress(event);
    });
  }

  // Handle keyboard input
  handleKeyPress(event) {
    if (this.gameState === "battle" && this.battleSystem) {
      this.battleSystem.handleKeyPress(event);
    }
  }

  // Show a specific screen
  showScreen(screenId) {
    // Hide all screens
    document.querySelectorAll(".screen").forEach((screen) => {
      screen.classList.remove("active");
    });

    // Show target screen
    const targetScreen = document.getElementById(screenId);
    if (targetScreen) {
      targetScreen.classList.add("active");
      this.currentScreen = screenId;
    }
  }

  // Update loading text
  updateLoadingText(text) {
    const loadingText = document.getElementById("loading-text");
    if (loadingText) {
      loadingText.textContent = text;
    }
  }

  // Select trainer and start battle
  async selectTrainer(difficulty) {
    const trainerKey = difficulty.toUpperCase();
    this.selectedTrainer = CONFIG.TRAINERS[trainerKey];

    if (!this.selectedTrainer) {
      console.error("Invalid trainer selected:", difficulty);
      return;
    }

    console.log("Selected trainer:", this.selectedTrainer.name);
    await this.startBattle();
  }

  // Start a battle
  async startBattle() {
    if (!this.selectedTrainer) {
      console.error("No trainer selected");
      return;
    }

    try {
      this.showScreen("loading");
      this.updateLoadingText("Preparing for battle...");

      // Create enemy team
      const enemyTeam = await createPokemonTeam(this.selectedTrainer.team);

      if (enemyTeam.length === 0) {
        throw new Error("Failed to create enemy team");
      }

      // Reset player team
      this.playerTeam.forEach((pokemon) => pokemon.reset());

      // Initialize battle system
      this.updateLoadingText("Starting battle...");

      // Create battle system (will be implemented in battle-system.js)
      this.battleSystem = new BattleSystem(
        this.playerTeam,
        enemyTeam,
        this.selectedTrainer
      );

      // Show battle screen
      this.showScreen("battle-screen");
      this.gameState = "battle";

      // Start the battle
      await this.battleSystem.startBattle();
    } catch (error) {
      console.error("Error starting battle:", error);
      this.updateLoadingText("Error starting battle. Returning to menu...");
      setTimeout(() => {
        this.showScreen("main-menu");
      }, 2000);
    }
  }

  // Handle battle end
  onBattleEnd(result) {
    this.gameState = "result";

    const resultTitle = document.getElementById("result-title");
    const resultMessage = document.getElementById("result-message");

    if (result.winner === "player") {
      resultTitle.textContent = "Victory!";
      resultMessage.textContent = `You defeated ${this.selectedTrainer.name}!`;
    } else {
      resultTitle.textContent = "Defeat!";
      resultMessage.textContent = `You were defeated by ${this.selectedTrainer.name}!`;
    }

    this.showScreen("battle-result");
  }

  // Get current game state
  getGameState() {
    return {
      currentScreen: this.currentScreen,
      gameState: this.gameState,
      playerTeam: this.playerTeam,
      selectedTrainer: this.selectedTrainer,
    };
  }
}

// The BattleSystem class is now implemented in battle-system.js

// Initialize game when page loads
document.addEventListener("DOMContentLoaded", () => {
  window.gameController = new GameController();
});
