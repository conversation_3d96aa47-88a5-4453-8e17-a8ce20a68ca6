<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pokemon Battle Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        padding: 20px;
        background: #f0f0f0;
      }
      .test-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .test-result {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
      }
      .success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #0056b3;
      }
      #console-output {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 400px;
        overflow-y: auto;
      }
      .battle-frame {
        width: 100%;
        height: 600px;
        border: 2px solid #333;
        border-radius: 8px;
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>Pokemon Battle Test</h1>

      <div class="test-result info">
        <strong>Testing battle system integration...</strong>
      </div>

      <div id="test-results"></div>

      <div>
        <button onclick="testBattleSystem()">Test Battle System</button>
        <button onclick="testPokemonCreation()">Test Pokemon Creation</button>
        <button onclick="testEvolutionSystem()">Test Evolution System</button>
        <button onclick="testLevelMoves()">Test Level-Appropriate Moves</button>
        <button onclick="testUniqueMoves()">Test Unique Moves Fix</button>
        <button onclick="testComprehensiveBattle()">
          Test Complete Battle
        </button>
        <button onclick="testDamageCalculation()">
          Test Damage Calculation
        </button>
        <button onclick="testTypeEffectiveness()">
          Test Type Effectiveness
        </button>
        <button onclick="openMainGame()">Open Main Game</button>
        <button onclick="clearMoveCache()">Clear Move Cache</button>
        <button onclick="clearConsole()">Clear Console</button>
      </div>

      <h3>Console Output:</h3>
      <div id="console-output"></div>

      <h3>Main Game (for testing):</h3>
      <iframe src="index.html" class="battle-frame"></iframe>
    </div>

    <!-- Include our game scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/pokemon.js"></script>
    <script src="js/battle-system.js"></script>
    <script src="js/ai.js"></script>
    <script src="js/ui.js"></script>

    <script>
      // Override console.log to capture output
      const originalLog = console.log;
      const originalError = console.error;
      const consoleOutput = document.getElementById("console-output");

      function logToPage(message, type = "log") {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = type === "error" ? "[ERROR]" : "[LOG]";
        consoleOutput.textContent += `${timestamp} ${prefix} ${message}\n`;
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
      }

      console.log = function (...args) {
        originalLog.apply(console, args);
        logToPage(args.join(" "));
      };

      console.error = function (...args) {
        originalError.apply(console, args);
        logToPage(args.join(" "), "error");
      };

      function addTestResult(message, success = true) {
        const resultsDiv = document.getElementById("test-results");
        const div = document.createElement("div");
        div.className = `test-result ${success ? "success" : "error"}`;
        div.innerHTML = `<strong>${success ? "✓" : "✗"}</strong> ${message}`;
        resultsDiv.appendChild(div);
      }

      function clearConsole() {
        consoleOutput.textContent = "";
        document.getElementById("test-results").innerHTML = "";
      }

      function clearMoveCache() {
        pokemonAPI.clearMoveCache();
        addTestResult(
          "Move cache cleared - next Pokemon creation will fetch fresh move data"
        );
      }

      async function testBattleSystem() {
        console.log("Testing Battle System...");

        try {
          // Test if BattleSystem class exists
          if (typeof BattleSystem === "undefined") {
            addTestResult("BattleSystem class not found", false);
            return;
          }

          // Create test Pokemon
          const testPokemon1 = await createPokemonFromId(25, 50); // Pikachu
          const testPokemon2 = await createPokemonFromId(6, 50); // Charizard

          if (!testPokemon1 || !testPokemon2) {
            addTestResult("Failed to create test Pokemon", false);
            return;
          }

          addTestResult(
            `Created test Pokemon: ${testPokemon1.name} vs ${testPokemon2.name}`
          );

          // Test battle system creation
          const playerTeam = [testPokemon1];
          const enemyTeam = [testPokemon2];
          const trainer = CONFIG.TRAINERS.BASIC;

          const battleSystem = new BattleSystem(playerTeam, enemyTeam, trainer);
          addTestResult("BattleSystem created successfully");

          console.log("Battle system test completed successfully");
        } catch (error) {
          addTestResult(`Battle system test failed: ${error.message}`, false);
          console.error("Battle system test error:", error);
        }
      }

      async function testPokemonCreation() {
        console.log("Testing Pokemon creation...");

        try {
          const pokemon = await createPokemonFromId(1, 50); // Bulbasaur
          if (pokemon) {
            addTestResult(
              `Pokemon created: ${pokemon.name} (Level ${pokemon.level})`
            );
            console.log("Pokemon stats:", pokemon.stats);
            console.log(
              "Pokemon moves:",
              pokemon.moves.map((m) => m.name)
            );
            console.log("Pokemon types:", pokemon.types);
          } else {
            addTestResult("Pokemon creation returned null", false);
          }
        } catch (error) {
          addTestResult(`Pokemon creation failed: ${error.message}`, false);
          console.error("Pokemon creation error:", error);
        }
      }

      async function testDamageCalculation() {
        console.log("Testing damage calculation...");

        try {
          const attacker = await createPokemonFromId(6, 50); // Charizard
          const defender = await createPokemonFromId(3, 50); // Venusaur

          if (!attacker || !defender) {
            addTestResult("Failed to create Pokemon for damage test", false);
            return;
          }

          const move = attacker.moves.find((m) => m.power && m.power > 0);
          if (!move) {
            addTestResult("No damaging moves found", false);
            return;
          }

          const damage = calculateDamage(attacker, defender, move);
          addTestResult(
            `Damage calculation: ${attacker.name} using ${move.name} deals ${damage} damage to ${defender.name}`
          );

          console.log("Damage calculation details:");
          console.log("- Attacker:", attacker.name, "Level:", attacker.level);
          console.log("- Defender:", defender.name, "Level:", defender.level);
          console.log(
            "- Move:",
            move.name,
            "Power:",
            move.power,
            "Type:",
            move.type
          );
          console.log("- Calculated damage:", damage);
        } catch (error) {
          addTestResult(`Damage calculation failed: ${error.message}`, false);
          console.error("Damage calculation error:", error);
        }
      }

      async function testTypeEffectiveness() {
        console.log("Testing type effectiveness...");

        try {
          // Test some known type matchups
          const fireVsGrass = getTypeEffectiveness("fire", ["grass"]);
          const waterVsFire = getTypeEffectiveness("water", ["fire"]);
          const electricVsGround = getTypeEffectiveness("electric", ["ground"]);
          const normalVsGhost = getTypeEffectiveness("normal", ["ghost"]);

          addTestResult(`Fire vs Grass: ${fireVsGrass}x (expected: 2x)`);
          addTestResult(`Water vs Fire: ${waterVsFire}x (expected: 2x)`);
          addTestResult(
            `Electric vs Ground: ${electricVsGround}x (expected: 0x)`
          );
          addTestResult(`Normal vs Ghost: ${normalVsGhost}x (expected: 0x)`);

          const allCorrect =
            fireVsGrass === 2 &&
            waterVsFire === 2 &&
            electricVsGround === 0 &&
            normalVsGhost === 0;

          if (allCorrect) {
            addTestResult("Type effectiveness system working correctly");
          } else {
            addTestResult("Type effectiveness system has issues", false);
          }
        } catch (error) {
          addTestResult(
            `Type effectiveness test failed: ${error.message}`,
            false
          );
          console.error("Type effectiveness error:", error);
        }
      }

      async function testEvolutionSystem() {
        console.log("Testing evolution system...");

        try {
          // Test Bulbasaur evolution line
          console.log("Testing Bulbasaur evolution line...");

          // Level 10 should be Bulbasaur
          const bulbasaur = await createPokemonFromId(1, 10);
          addTestResult(`Level 10: ${bulbasaur.name} (expected: bulbasaur)`);

          // Level 20 should be Ivysaur (evolves at 16)
          const ivysaur = await createPokemonFromId(1, 20);
          addTestResult(`Level 20: ${ivysaur.name} (expected: ivysaur)`);

          // Level 40 should be Venusaur (evolves at 32)
          const venusaur = await createPokemonFromId(1, 40);
          addTestResult(`Level 40: ${venusaur.name} (expected: venusaur)`);

          // Test Charmander evolution line
          console.log("Testing Charmander evolution line...");

          // Level 10 should be Charmander
          const charmander = await createPokemonFromId(4, 10);
          addTestResult(`Level 10: ${charmander.name} (expected: charmander)`);

          // Level 20 should be Charmeleon (evolves at 16)
          const charmeleon = await createPokemonFromId(4, 20);
          addTestResult(`Level 20: ${charmeleon.name} (expected: charmeleon)`);

          // Level 40 should be Charizard (evolves at 36)
          const charizard = await createPokemonFromId(4, 40);
          addTestResult(`Level 40: ${charizard.name} (expected: charizard)`);

          // Verify that high-level Pokemon are correctly evolved
          const evolutionCorrect =
            bulbasaur.name === "bulbasaur" &&
            ivysaur.name === "ivysaur" &&
            venusaur.name === "venusaur" &&
            charmander.name === "charmander" &&
            charmeleon.name === "charmeleon" &&
            charizard.name === "charizard";

          if (evolutionCorrect) {
            addTestResult("Evolution system working correctly!");
          } else {
            addTestResult("Evolution system has issues", false);
          }
        } catch (error) {
          addTestResult(`Evolution test failed: ${error.message}`, false);
          console.error("Evolution test error:", error);
        }
      }

      async function testLevelMoves() {
        console.log("Testing level-appropriate moves...");

        try {
          // Test a low-level Pokemon
          const lowLevelPokemon = await createPokemonFromId(1, 5); // Bulbasaur level 5
          console.log(
            `${lowLevelPokemon.name} level ${lowLevelPokemon.level} moves:`,
            lowLevelPokemon.moves.map(
              (m) => `${m.name} (learned at ${m.learnedAtLevel || "unknown"})`
            )
          );

          // Test a high-level Pokemon
          const highLevelPokemon = await createPokemonFromId(1, 50); // Should be Venusaur
          console.log(
            `${highLevelPokemon.name} level ${highLevelPokemon.level} moves:`,
            highLevelPokemon.moves.map(
              (m) => `${m.name} (learned at ${m.learnedAtLevel || "unknown"})`
            )
          );

          // Verify moves are appropriate for level
          const lowLevelMovesValid = lowLevelPokemon.moves.every(
            (move) =>
              !move.learnedAtLevel ||
              move.learnedAtLevel <= lowLevelPokemon.level
          );

          const highLevelMovesValid = highLevelPokemon.moves.every(
            (move) =>
              !move.learnedAtLevel ||
              move.learnedAtLevel <= highLevelPokemon.level
          );

          addTestResult(`Low-level moves valid: ${lowLevelMovesValid}`);
          addTestResult(`High-level moves valid: ${highLevelMovesValid}`);

          if (lowLevelMovesValid && highLevelMovesValid) {
            addTestResult("Level-appropriate moves system working correctly!");
          } else {
            addTestResult("Level-appropriate moves system has issues", false);
          }
        } catch (error) {
          addTestResult(`Level moves test failed: ${error.message}`, false);
          console.error("Level moves test error:", error);
        }
      }

      async function testUniqueMoves() {
        console.log("Testing unique moves fix...");

        try {
          // Test Venusaur at level 50 - should have unique moves
          const venusaur = await createPokemonFromId(1, 50); // Should be Venusaur

          addTestResult(`Created: ${venusaur.name} (Level ${venusaur.level})`);

          // Check that all moves are unique
          const moveNames = venusaur.moves.map((m) => m.name);
          const uniqueMoveNames = [...new Set(moveNames)];

          addTestResult(`Total moves: ${moveNames.length}`);
          addTestResult(`Unique moves: ${uniqueMoveNames.length}`);
          addTestResult(`Moves: ${moveNames.join(", ")}`);

          const allMovesUnique = moveNames.length === uniqueMoveNames.length;

          if (allMovesUnique) {
            addTestResult("✅ All moves are unique!");
          } else {
            addTestResult("❌ Duplicate moves found!", false);
          }

          // Test that moves are appropriate for level
          const validLevels = venusaur.moves.every(
            (move) =>
              !move.learnedAtLevel || move.learnedAtLevel <= venusaur.level
          );

          if (validLevels) {
            addTestResult("✅ All moves learned at appropriate levels!");
          } else {
            addTestResult("❌ Some moves learned at invalid levels!", false);
          }

          // Show move details
          venusaur.moves.forEach((move) => {
            console.log(
              `- ${move.name} (Level ${move.learnedAtLevel}, Power: ${
                move.power || "N/A"
              })`
            );
          });
        } catch (error) {
          addTestResult(`Unique moves test failed: ${error.message}`, false);
          console.error("Unique moves test error:", error);
        }
      }

      async function testComprehensiveBattle() {
        console.log(
          "Testing comprehensive battle with evolution and level-appropriate moves..."
        );

        try {
          // Create evolved Pokemon with level-appropriate moves
          const playerPokemon = await createPokemonFromId(1, 35); // Should be Venusaur
          const enemyPokemon = await createPokemonFromId(4, 40); // Should be Charizard

          addTestResult(
            `Player: ${playerPokemon.name} (Level ${playerPokemon.level})`
          );
          addTestResult(
            `Enemy: ${enemyPokemon.name} (Level ${enemyPokemon.level})`
          );

          // Verify they are evolved forms
          const correctEvolution =
            playerPokemon.name === "venusaur" &&
            enemyPokemon.name === "charizard";
          addTestResult(`Correct evolution forms: ${correctEvolution}`);

          // Test battle system with these Pokemon
          const playerTeam = [playerPokemon];
          const enemyTeam = [enemyPokemon];
          const trainer = CONFIG.TRAINERS.STANDARD;

          const battleSystem = new BattleSystem(playerTeam, enemyTeam, trainer);
          addTestResult("Battle system created with evolved Pokemon");

          // Test damage calculation between evolved Pokemon
          const move = playerPokemon.moves.find((m) => m.power && m.power > 0);
          if (move) {
            const damage = calculateDamage(playerPokemon, enemyPokemon, move);
            addTestResult(
              `${playerPokemon.name} using ${move.name} deals ${damage} damage to ${enemyPokemon.name}`
            );

            // Test type effectiveness (Grass vs Fire should be not very effective)
            const effectiveness = getTypeEffectiveness(
              move.type,
              enemyPokemon.types
            );
            addTestResult(
              `Type effectiveness: ${move.type} vs ${enemyPokemon.types.join(
                "/"
              )} = ${effectiveness}x`
            );
          }

          addTestResult("Comprehensive battle test completed successfully!");
        } catch (error) {
          addTestResult(
            `Comprehensive battle test failed: ${error.message}`,
            false
          );
          console.error("Comprehensive battle test error:", error);
        }
      }

      function openMainGame() {
        window.open("index.html", "_blank");
      }

      // Run initial tests
      window.addEventListener("load", () => {
        setTimeout(() => {
          console.log("Starting automated tests...");
          testTypeEffectiveness();
        }, 1000);
      });
    </script>
  </body>
</html>
