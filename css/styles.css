/* Pokemon Battle Game Styles */

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Press Start 2P", monospace;
  font-size: 12px;
  line-height: 1.4;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Screen Management */
.screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.screen.active {
  display: flex;
}

/* Loading Screen */
#loading-screen {
  background: linear-gradient(45deg, #1e3c72, #2a5298);
  color: white;
  flex-direction: column;
}

.loading-content {
  text-align: center;
  padding: 2rem;
}

.loading-content h1 {
  font-size: 24px;
  margin-bottom: 2rem;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

#loading-text {
  font-size: 10px;
  color: #ccc;
}

/* Main Menu */
#main-menu {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  flex-direction: column;
}

.menu-content {
  text-align: center;
  padding: 2rem;
}

.menu-content h1 {
  font-size: 28px;
  margin-bottom: 3rem;
  color: #ffd700;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
}

.menu-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.menu-button {
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-family: inherit;
  font-size: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.menu-button:hover {
  background: linear-gradient(45deg, #45a049, #4caf50);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.menu-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Trainer Selection */
#trainer-selection {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  flex-direction: column;
}

.trainer-content {
  text-align: center;
  padding: 2rem;
  max-width: 800px;
  width: 100%;
}

.trainer-content h2 {
  font-size: 20px;
  margin-bottom: 2rem;
  color: #ffd700;
}

.trainer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.trainer-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.trainer-card:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffd700;
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.trainer-sprite {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  padding: 8px;
}

.trainer-card h3 {
  font-size: 12px;
  margin-bottom: 0.5rem;
  color: #ffd700;
}

.trainer-card p {
  font-size: 10px;
  color: #ccc;
}

/* Battle Screen */
#battle-screen {
  background: linear-gradient(to bottom, #87ceeb 0%, #98fb98 50%, #90ee90 100%);
  flex-direction: column;
  padding: 0;
  justify-content: space-between;
}

.battle-field {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  padding: 1rem;
  position: relative;
  gap: 1rem;
}

/* Pokemon Areas */
.pokemon-area {
  display: flex;
  position: relative;
}

/* Enemy area - top right for Pokemon, top left for info */
.enemy-area {
  grid-column: 2;
  grid-row: 1;
  justify-content: flex-end;
  align-items: flex-start;
  flex-direction: row-reverse;
}

/* Player area - bottom left for Pokemon, bottom right for info */
.player-area {
  grid-column: 1;
  grid-row: 2;
  justify-content: flex-start;
  align-items: flex-end;
}

/* Enemy info area - top left */
.enemy-info-area {
  grid-column: 1;
  grid-row: 1;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

/* Player info area - bottom right */
.player-info-area {
  grid-column: 2;
  grid-row: 2;
  justify-content: flex-end;
  align-items: flex-end;
  display: flex;
}

.pokemon-sprite-container {
  position: relative;
  z-index: 2;
}

.pokemon-sprite {
  width: 120px;
  height: 120px;
  object-fit: contain;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.pokemon-sprite.damaged {
  animation: damage-flash 0.5s ease-in-out;
}

.pokemon-sprite.fainted {
  animation: faint-fall 1s ease-in-out forwards;
}

@keyframes damage-flash {
  0%,
  100% {
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  }
  50% {
    filter: drop-shadow(2px 2px 4px rgba(255, 0, 0, 0.8)) brightness(1.5);
  }
}

@keyframes faint-fall {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(10px) rotate(-15deg);
    opacity: 0.7;
  }
  100% {
    transform: translateY(30px) rotate(-90deg);
    opacity: 0.3;
  }
}

/* Pokemon Info */
.pokemon-info {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #333;
  border-radius: 8px;
  padding: 1rem;
  margin: 0 1rem;
  min-width: 200px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.pokemon-name-level {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 10px;
  color: #333;
}

.hp-bar-container {
  margin-bottom: 0.5rem;
}

.hp-bar {
  width: 100%;
  height: 8px;
  background: #ddd;
  border: 1px solid #333;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.hp-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  transition: width 0.5s ease, background 0.3s ease;
}

.hp-fill.low {
  background: linear-gradient(90deg, #ff5722, #ff9800);
}

.hp-fill.critical {
  background: linear-gradient(90deg, #f44336, #ff5722);
  animation: critical-pulse 1s ease-in-out infinite alternate;
}

@keyframes critical-pulse {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.hp-text {
  font-size: 8px;
  color: #333;
  text-align: right;
}

.status-indicators {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.status-indicator {
  font-size: 6px;
  padding: 2px 4px;
  border-radius: 3px;
  color: white;
  font-weight: bold;
}

.status-poison {
  background: #9c27b0;
}
.status-burn {
  background: #ff5722;
}
.status-paralysis {
  background: #ffc107;
  color: #333;
}
.status-sleep {
  background: #607d8b;
}
.status-freeze {
  background: #03a9f4;
}

/* Text Box */
.text-box {
  background: rgba(255, 255, 255, 0.95);
  border: 3px solid #333;
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem;
  min-height: 60px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#battle-text {
  color: #333;
  font-size: 10px;
  line-height: 1.4;
}

/* Menu Containers */
.menu-container {
  background: rgba(44, 62, 80, 0.95);
  border-top: 3px solid #333;
  padding: 1rem;
  display: none;
  backdrop-filter: blur(5px);
}

.menu-container.active {
  display: block;
}

/* Action Menu */
.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  max-width: 400px;
  margin: 0 auto;
}

.action-button {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-family: inherit;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: manipulation;
}

.action-button:hover {
  background: linear-gradient(45deg, #2980b9, #3498db);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.action-button:active {
  transform: translateY(0);
}

/* Move Menu */
.move-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
  max-width: 500px;
  margin: 0 auto 1rem;
}

.move-button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1rem;
  border-radius: 8px;
  font-family: inherit;
  font-size: 9px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.move-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffd700;
  transform: translateY(-2px);
}

.move-name {
  font-size: 10px;
  margin-bottom: 0.5rem;
  color: #ffd700;
}

.move-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 8px;
}

.move-type {
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 7px;
  text-transform: uppercase;
}

/* Pokemon Type Colors */
.move-type.normal {
  background: #a8a878;
}
.move-type.fire {
  background: #f08030;
}
.move-type.water {
  background: #6890f0;
}
.move-type.electric {
  background: #f8d030;
}
.move-type.grass {
  background: #78c850;
}
.move-type.ice {
  background: #98d8d8;
}
.move-type.fighting {
  background: #c03028;
}
.move-type.poison {
  background: #a040a0;
}
.move-type.ground {
  background: #e0c068;
}
.move-type.flying {
  background: #a890f0;
}
.move-type.psychic {
  background: #f85888;
}
.move-type.bug {
  background: #a8b820;
}
.move-type.rock {
  background: #b8a038;
}
.move-type.ghost {
  background: #705898;
}
.move-type.dragon {
  background: #7038f8;
}
.move-type.dark {
  background: #705848;
}
.move-type.steel {
  background: #b8b8d0;
}
.move-type.fairy {
  background: #ee99ac;
}

.move-pp {
  color: #ccc;
}

/* Back Button */
.back-button {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-family: inherit;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;
}

.back-button:hover {
  background: linear-gradient(45deg, #c0392b, #e74c3c);
  transform: translateY(-2px);
}

/* Battle Result */
#battle-result {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  flex-direction: column;
}

.result-content {
  text-align: center;
  padding: 2rem;
}

#result-title {
  font-size: 24px;
  margin-bottom: 1rem;
  color: #ffd700;
}

#result-message {
  font-size: 12px;
  margin-bottom: 2rem;
  color: #ccc;
}

.result-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

/* Pokemon Team Menu */
.pokemon-team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto 1rem;
}

.pokemon-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.pokemon-card:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffd700;
  transform: translateY(-2px);
}

.pokemon-card.active {
  border-color: #4caf50;
  background: rgba(76, 175, 80, 0.2);
}

.pokemon-card.fainted {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(100%);
}

.pokemon-card-sprite {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.pokemon-card-sprite img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.pokemon-card-info {
  flex: 1;
  color: white;
}

.pokemon-card-name {
  font-size: 12px;
  color: #ffd700;
  margin-bottom: 0.25rem;
  text-transform: capitalize;
}

.pokemon-card-level {
  font-size: 10px;
  color: #ccc;
  margin-bottom: 0.25rem;
}

.pokemon-card-hp {
  font-size: 9px;
  color: #ccc;
  margin-bottom: 0.25rem;
}

.pokemon-card-status {
  font-size: 8px;
  color: #ff6b6b;
}

/* Bag Menu */
.bag-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
  max-width: 400px;
  margin: 0 auto 1rem;
}

.item-button {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 1rem;
  border-radius: 8px;
  font-family: inherit;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.item-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffd700;
  transform: translateY(-2px);
}

.item-name {
  font-size: 11px;
  margin-bottom: 0.5rem;
  color: #ffd700;
}

.item-count {
  font-size: 9px;
  color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    font-size: 10px;
  }

  .pokemon-sprite {
    width: 100px;
    height: 100px;
  }

  .pokemon-info {
    min-width: 150px;
    padding: 0.75rem;
  }

  .action-grid {
    gap: 0.75rem;
  }

  .move-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .trainer-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .battle-field {
    padding: 0.5rem;
  }

  .pokemon-area {
    height: 35%;
  }

  .pokemon-sprite {
    width: 80px;
    height: 80px;
  }

  .pokemon-info {
    min-width: 120px;
    padding: 0.5rem;
    margin: 0 0.5rem;
  }

  .text-box {
    margin: 0.5rem;
    padding: 0.75rem;
    min-height: 50px;
  }

  .menu-container {
    padding: 0.75rem;
  }
}
