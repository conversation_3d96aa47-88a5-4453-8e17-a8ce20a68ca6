#!/usr/bin/env node

/**
 * Generate Index Files Script
 * Creates index files for faster offline data lookups
 */

const fs = require('fs').promises;
const path = require('path');

class IndexGenerator {
  constructor() {
    this.dataDir = path.join(__dirname, '..', 'data');
  }

  async generatePokemonIndex() {
    console.log('Generating Pokemon index...');
    
    const pokemonDir = path.join(this.dataDir, 'pokemon');
    const files = await fs.readdir(pokemonDir);
    
    const pokemonIndex = [];
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(pokemonDir, file);
          const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
          
          pokemonIndex.push({
            id: data.id,
            name: data.name,
            filename: file,
            types: data.types
          });
        } catch (error) {
          console.error(`Error processing ${file}:`, error.message);
        }
      }
    }
    
    // Sort by ID
    pokemonIndex.sort((a, b) => a.id - b.id);
    
    const indexPath = path.join(this.dataDir, 'metadata', 'pokemon-index.json');
    await fs.writeFile(indexPath, JSON.stringify(pokemonIndex, null, 2));
    
    console.log(`✓ Generated Pokemon index with ${pokemonIndex.length} entries`);
    return pokemonIndex;
  }

  async generateSpeciesIndex() {
    console.log('Generating Species index...');
    
    const speciesDir = path.join(this.dataDir, 'species');
    const files = await fs.readdir(speciesDir);
    
    const speciesIndex = [];
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(speciesDir, file);
          const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
          
          speciesIndex.push({
            id: data.id,
            name: data.name,
            filename: file,
            evolutionChainUrl: data.evolution_chain?.url
          });
        } catch (error) {
          console.error(`Error processing ${file}:`, error.message);
        }
      }
    }
    
    // Sort by ID
    speciesIndex.sort((a, b) => a.id - b.id);
    
    const indexPath = path.join(this.dataDir, 'metadata', 'species-index.json');
    await fs.writeFile(indexPath, JSON.stringify(speciesIndex, null, 2));
    
    console.log(`✓ Generated Species index with ${speciesIndex.length} entries`);
    return speciesIndex;
  }

  async generateMovesIndex() {
    console.log('Generating Moves index...');
    
    const movesDir = path.join(this.dataDir, 'moves');
    const files = await fs.readdir(movesDir);
    
    const movesIndex = [];
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(movesDir, file);
          const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
          
          movesIndex.push({
            name: data.name,
            filename: file,
            type: data.type,
            power: data.power,
            accuracy: data.accuracy,
            damageClass: data.damage_class
          });
        } catch (error) {
          console.error(`Error processing ${file}:`, error.message);
        }
      }
    }
    
    // Sort by name
    movesIndex.sort((a, b) => a.name.localeCompare(b.name));
    
    const indexPath = path.join(this.dataDir, 'metadata', 'moves-index.json');
    await fs.writeFile(indexPath, JSON.stringify(movesIndex, null, 2));
    
    console.log(`✓ Generated Moves index with ${movesIndex.length} entries`);
    return movesIndex;
  }

  async generateEvolutionChainsIndex() {
    console.log('Generating Evolution Chains index...');
    
    const chainsDir = path.join(this.dataDir, 'evolution-chains');
    const files = await fs.readdir(chainsDir);
    
    const chainsIndex = [];
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(chainsDir, file);
          const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
          
          // Extract chain ID from filename
          const chainId = file.replace('chain-', '').replace('.json', '');
          
          chainsIndex.push({
            id: parseInt(chainId),
            filename: file,
            baseSpecies: data.chain?.species?.name
          });
        } catch (error) {
          console.error(`Error processing ${file}:`, error.message);
        }
      }
    }
    
    // Sort by ID
    chainsIndex.sort((a, b) => a.id - b.id);
    
    const indexPath = path.join(this.dataDir, 'metadata', 'evolution-chains-index.json');
    await fs.writeFile(indexPath, JSON.stringify(chainsIndex, null, 2));
    
    console.log(`✓ Generated Evolution Chains index with ${chainsIndex.length} entries`);
    return chainsIndex;
  }

  async generateAllIndexes() {
    console.log('Generating all index files...\n');
    
    try {
      // Ensure metadata directory exists
      const metadataDir = path.join(this.dataDir, 'metadata');
      await fs.mkdir(metadataDir, { recursive: true });
      
      // Generate all indexes
      const pokemonIndex = await this.generatePokemonIndex();
      const speciesIndex = await this.generateSpeciesIndex();
      const movesIndex = await this.generateMovesIndex();
      const chainsIndex = await this.generateEvolutionChainsIndex();
      
      // Generate summary
      const summary = {
        generatedAt: new Date().toISOString(),
        indexes: {
          pokemon: pokemonIndex.length,
          species: speciesIndex.length,
          moves: movesIndex.length,
          evolutionChains: chainsIndex.length
        }
      };
      
      const summaryPath = path.join(this.dataDir, 'metadata', 'indexes-summary.json');
      await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2));
      
      console.log('\n✅ All index files generated successfully!');
      console.log(`📊 Summary: ${summary.indexes.pokemon} Pokemon, ${summary.indexes.species} species, ${summary.indexes.moves} moves, ${summary.indexes.evolutionChains} evolution chains`);
      
    } catch (error) {
      console.error('\n❌ Index generation failed:', error.message);
      throw error;
    }
  }
}

// Run the index generator if this script is executed directly
if (require.main === module) {
  const generator = new IndexGenerator();
  
  generator.generateAllIndexes()
    .then(() => {
      console.log('Index generation completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Index generation failed:', error);
      process.exit(1);
    });
}

module.exports = IndexGenerator;
