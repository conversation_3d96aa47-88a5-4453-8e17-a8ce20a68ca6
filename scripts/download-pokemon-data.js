#!/usr/bin/env node

/**
 * Pokemon Data Download Script
 * Downloads all necessary Pokemon data from PokeAPI for offline use
 */

const fs = require('fs').promises;
const path = require('path');

class PokemonDataDownloader {
  constructor() {
    this.baseUrl = 'https://pokeapi.co/api/v2';
    this.dataDir = path.join(__dirname, '..', 'data');
    this.requestDelay = 100; // ms between requests to avoid rate limiting
    this.downloadedMoves = new Set();
    this.downloadedEvolutionChains = new Set();
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async fetchWithRetry(url, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`Fetching: ${url}`);
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
      } catch (error) {
        console.error(`Attempt ${i + 1} failed for ${url}:`, error.message);
        if (i === maxRetries - 1) throw error;
        await this.delay(1000 * (i + 1)); // Exponential backoff
      }
    }
  }

  async ensureDirectoryExists(dirPath) {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  async saveJsonFile(filePath, data) {
    await this.ensureDirectoryExists(path.dirname(filePath));
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
  }

  normalizeMoveName(name) {
    return name.toLowerCase().replace(/[^a-z0-9]/g, '-');
  }

  async downloadPokemonData(id) {
    try {
      // Fetch basic Pokemon data
      const pokemonData = await this.fetchWithRetry(`${this.baseUrl}/pokemon/${id}`);
      
      // Process and filter data to only include what the game uses
      const processedPokemon = {
        id: pokemonData.id,
        name: pokemonData.name,
        types: pokemonData.types.map(type => type.type.name),
        baseStats: {
          hp: pokemonData.stats.find(stat => stat.stat.name === 'hp').base_stat,
          attack: pokemonData.stats.find(stat => stat.stat.name === 'attack').base_stat,
          defense: pokemonData.stats.find(stat => stat.stat.name === 'defense').base_stat,
          specialAttack: pokemonData.stats.find(stat => stat.stat.name === 'special-attack').base_stat,
          specialDefense: pokemonData.stats.find(stat => stat.stat.name === 'special-defense').base_stat,
          speed: pokemonData.stats.find(stat => stat.stat.name === 'speed').base_stat,
        },
        sprites: {
          front: pokemonData.sprites.front_default,
          back: pokemonData.sprites.back_default,
          frontShiny: pokemonData.sprites.front_shiny,
          backShiny: pokemonData.sprites.back_shiny,
        },
        height: pokemonData.height,
        weight: pokemonData.weight,
        moves: pokemonData.moves.map(move => ({
          name: move.move.name,
          url: move.move.url,
          version_group_details: move.version_group_details
        }))
      };

      // Save Pokemon data
      const pokemonFileName = `${id.toString().padStart(3, '0')}-${pokemonData.name}.json`;
      const pokemonFilePath = path.join(this.dataDir, 'pokemon', pokemonFileName);
      await this.saveJsonFile(pokemonFilePath, processedPokemon);

      console.log(`✓ Downloaded Pokemon: ${pokemonData.name} (${id})`);
      return processedPokemon;
    } catch (error) {
      console.error(`✗ Failed to download Pokemon ${id}:`, error.message);
      throw error;
    }
  }

  async downloadSpeciesData(id) {
    try {
      const speciesData = await this.fetchWithRetry(`${this.baseUrl}/pokemon-species/${id}`);
      
      // Process species data
      const processedSpecies = {
        id: speciesData.id,
        name: speciesData.name,
        evolution_chain: {
          url: speciesData.evolution_chain.url
        }
      };

      // Save species data
      const speciesFileName = `${id.toString().padStart(3, '0')}-${speciesData.name}-species.json`;
      const speciesFilePath = path.join(this.dataDir, 'species', speciesFileName);
      await this.saveJsonFile(speciesFilePath, processedSpecies);

      console.log(`✓ Downloaded Species: ${speciesData.name} (${id})`);
      return processedSpecies;
    } catch (error) {
      console.error(`✗ Failed to download species ${id}:`, error.message);
      throw error;
    }
  }

  async downloadEvolutionChain(chainUrl) {
    const chainId = chainUrl.split('/').slice(-2, -1)[0];
    
    if (this.downloadedEvolutionChains.has(chainId)) {
      return; // Already downloaded
    }

    try {
      const evolutionData = await this.fetchWithRetry(chainUrl);
      
      // Save evolution chain data
      const chainFileName = `chain-${chainId}.json`;
      const chainFilePath = path.join(this.dataDir, 'evolution-chains', chainFileName);
      await this.saveJsonFile(chainFilePath, evolutionData);

      this.downloadedEvolutionChains.add(chainId);
      console.log(`✓ Downloaded Evolution Chain: ${chainId}`);
    } catch (error) {
      console.error(`✗ Failed to download evolution chain ${chainId}:`, error.message);
    }
  }

  async downloadMoveData(moveUrl) {
    const moveName = moveUrl.split('/').slice(-2, -1)[0];
    
    if (this.downloadedMoves.has(moveName)) {
      return; // Already downloaded
    }

    try {
      const moveData = await this.fetchWithRetry(moveUrl);
      
      // Process move data
      const processedMove = {
        name: moveData.name,
        power: moveData.power,
        accuracy: moveData.accuracy,
        pp: moveData.pp,
        type: moveData.type.name,
        damage_class: moveData.damage_class.name,
        effect_entries: moveData.effect_entries.filter(entry => entry.language.name === 'en')
      };

      // Save move data
      const moveFileName = `${this.normalizeMoveName(moveName)}.json`;
      const moveFilePath = path.join(this.dataDir, 'moves', moveFileName);
      await this.saveJsonFile(moveFilePath, processedMove);

      this.downloadedMoves.add(moveName);
      console.log(`✓ Downloaded Move: ${moveName}`);
    } catch (error) {
      console.error(`✗ Failed to download move ${moveName}:`, error.message);
    }
  }

  async downloadAllData(pokemonLimit = 151) {
    console.log(`Starting download of Pokemon data (1-${pokemonLimit})...`);
    console.log('This may take several minutes due to rate limiting.\n');

    const startTime = Date.now();
    const metadata = {
      version: '1.0.0',
      downloadDate: new Date().toISOString(),
      pokemonCount: pokemonLimit,
      source: 'PokeAPI v2'
    };

    try {
      // Download Pokemon and Species data
      for (let id = 1; id <= pokemonLimit; id++) {
        await this.downloadPokemonData(id);
        await this.delay(this.requestDelay);
        
        const speciesData = await this.downloadSpeciesData(id);
        await this.delay(this.requestDelay);
        
        // Download evolution chain if we have one
        if (speciesData.evolution_chain?.url) {
          await this.downloadEvolutionChain(speciesData.evolution_chain.url);
          await this.delay(this.requestDelay);
        }

        // Progress indicator
        if (id % 10 === 0) {
          console.log(`Progress: ${id}/${pokemonLimit} Pokemon completed`);
        }
      }

      console.log('\nDownloading move data...');
      
      // Download move data for all Pokemon
      const pokemonFiles = await fs.readdir(path.join(this.dataDir, 'pokemon'));
      for (const file of pokemonFiles) {
        const pokemonData = JSON.parse(
          await fs.readFile(path.join(this.dataDir, 'pokemon', file), 'utf8')
        );
        
        // Download moves (limit to first 20 to avoid too many requests)
        const movesToDownload = pokemonData.moves.slice(0, 20);
        for (const move of movesToDownload) {
          await this.downloadMoveData(move.url);
          await this.delay(this.requestDelay);
        }
      }

      // Save metadata
      metadata.movesCount = this.downloadedMoves.size;
      metadata.evolutionChainsCount = this.downloadedEvolutionChains.size;
      metadata.downloadDuration = Math.round((Date.now() - startTime) / 1000);
      
      await this.saveJsonFile(
        path.join(this.dataDir, 'metadata', 'download-info.json'),
        metadata
      );

      console.log('\n✅ Download completed successfully!');
      console.log(`📊 Downloaded: ${pokemonLimit} Pokemon, ${this.downloadedMoves.size} moves, ${this.downloadedEvolutionChains.size} evolution chains`);
      console.log(`⏱️  Total time: ${metadata.downloadDuration} seconds`);

    } catch (error) {
      console.error('\n❌ Download failed:', error.message);
      throw error;
    }
  }
}

// Run the downloader if this script is executed directly
if (require.main === module) {
  const downloader = new PokemonDataDownloader();
  const pokemonLimit = process.argv[2] ? parseInt(process.argv[2]) : 151;
  
  downloader.downloadAllData(pokemonLimit)
    .then(() => {
      console.log('Data download completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Data download failed:', error);
      process.exit(1);
    });
}

module.exports = PokemonDataDownloader;
