<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline Data Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #test-results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Pokemon Game - Offline Data Test</h1>
    
    <div>
        <button onclick="testOfflineDataLoader()">Test Offline Data Loader</button>
        <button onclick="testPokemonAPI()">Test Pokemon API with Offline</button>
        <button onclick="testEvolutionSystem()">Test Evolution System</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="test-results"></div>

    <!-- Include necessary scripts -->
    <script src="js/config.js"></script>
    <script src="js/offline-data-loader.js"></script>
    <script src="js/api.js"></script>
    <script src="js/pokemon.js"></script>

    <script>
        function addTestResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            console.log(message);
        }

        function addInfoResult(message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            console.log(message);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testOfflineDataLoader() {
            addInfoResult('Testing Offline Data Loader...');
            
            try {
                // Test if offline data loader is available
                if (typeof offlineDataLoader === 'undefined') {
                    addTestResult('Offline data loader not available', false);
                    return;
                }

                addTestResult('✓ Offline data loader is available');

                // Test offline data availability
                const hasOfflineData = await offlineDataLoader.checkOfflineDataAvailability();
                if (hasOfflineData) {
                    addTestResult('✓ Offline data is available');
                    
                    const stats = await offlineDataLoader.getOfflineDataStats();
                    if (stats) {
                        addInfoResult(`📊 Offline data stats: ${stats.pokemonCount} Pokemon, ${stats.movesCount} moves`);
                        addInfoResult(`📅 Downloaded: ${new Date(stats.downloadDate).toLocaleString()}`);
                    }
                } else {
                    addTestResult('⚠️ No offline data found - run download script first', false);
                }

                // Test loading a specific Pokemon
                try {
                    const pikachu = await offlineDataLoader.loadPokemon(25);
                    if (pikachu) {
                        addTestResult(`✓ Successfully loaded ${pikachu.name} from offline data`);
                        addInfoResult(`   Types: ${pikachu.types.join(', ')}`);
                        addInfoResult(`   Base HP: ${pikachu.baseStats.hp}`);
                    } else {
                        addTestResult('⚠️ Could not load Pikachu from offline data', false);
                    }
                } catch (error) {
                    addTestResult(`✗ Error loading Pokemon: ${error.message}`, false);
                }

            } catch (error) {
                addTestResult(`✗ Offline data loader test failed: ${error.message}`, false);
            }
        }

        async function testPokemonAPI() {
            addInfoResult('Testing Pokemon API with Offline Integration...');
            
            try {
                // Test if Pokemon API is available
                if (typeof pokemonAPI === 'undefined') {
                    addTestResult('Pokemon API not available', false);
                    return;
                }

                addTestResult('✓ Pokemon API is available');

                // Test loading Pokemon through API (should use offline data if available)
                const bulbasaur = await pokemonAPI.fetchPokemon(1);
                if (bulbasaur) {
                    addTestResult(`✓ Successfully loaded ${bulbasaur.name} through API`);
                    addInfoResult(`   Types: ${bulbasaur.types.join(', ')}`);
                    addInfoResult(`   Moves: ${bulbasaur.moves.length} moves loaded`);
                } else {
                    addTestResult('✗ Could not load Bulbasaur', false);
                }

                // Test species data
                const species = await pokemonAPI.fetchPokemonSpecies(1);
                if (species) {
                    addTestResult(`✓ Successfully loaded species data for ${species.name}`);
                } else {
                    addTestResult('✗ Could not load species data', false);
                }

                // Test evolution chain
                if (species && species.evolution_chain) {
                    const evolutionChain = await pokemonAPI.fetchEvolutionChain(species.evolution_chain.url);
                    if (evolutionChain) {
                        addTestResult('✓ Successfully loaded evolution chain');
                    } else {
                        addTestResult('✗ Could not load evolution chain', false);
                    }
                }

            } catch (error) {
                addTestResult(`✗ Pokemon API test failed: ${error.message}`, false);
            }
        }

        async function testEvolutionSystem() {
            addInfoResult('Testing Evolution System with Offline Data...');
            
            try {
                // Test evolution chain parsing
                const evolutionChain = await getEvolutionChain(1); // Bulbasaur
                if (evolutionChain) {
                    addTestResult('✓ Successfully loaded evolution chain for Bulbasaur');
                    
                    // Test getting correct Pokemon form at different levels
                    const level16Form = await getCorrectPokemonForm(1, 16); // Should be Ivysaur
                    const level32Form = await getCorrectPokemonForm(1, 32); // Should be Venusaur
                    
                    addInfoResult(`   Level 16 form: Pokemon ID ${level16Form}`);
                    addInfoResult(`   Level 32 form: Pokemon ID ${level32Form}`);
                    
                    if (level16Form !== 1 && level32Form !== level16Form) {
                        addTestResult('✓ Evolution system working correctly');
                    } else {
                        addTestResult('⚠️ Evolution system may not be working as expected', false);
                    }
                } else {
                    addTestResult('✗ Could not load evolution chain', false);
                }

                // Test level-appropriate moves
                const levelMoves = await getLevelAppropriateMoves(25, 20); // Pikachu at level 20
                if (levelMoves && levelMoves.length > 0) {
                    addTestResult(`✓ Successfully loaded ${levelMoves.length} level-appropriate moves for Pikachu`);
                    addInfoResult(`   Moves: ${levelMoves.map(m => m.name).join(', ')}`);
                } else {
                    addTestResult('⚠️ No level-appropriate moves found', false);
                }

            } catch (error) {
                addTestResult(`✗ Evolution system test failed: ${error.message}`, false);
            }
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            addInfoResult('🎮 Pokemon Game Offline Data Test Ready');
            addInfoResult('Click the buttons above to test different components');
            
            // Check if we're running from a web server
            if (location.protocol === 'file:') {
                addTestResult('⚠️ Running from file:// protocol - offline data may not work. Please use a web server.', false);
            } else {
                addTestResult('✓ Running from web server - offline data should work');
            }
        });
    </script>
</body>
</html>
